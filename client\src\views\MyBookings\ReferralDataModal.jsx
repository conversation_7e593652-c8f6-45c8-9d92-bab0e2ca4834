import { Button, Modal, Table } from 'react-bootstrap';
import { ModifyType, ReferralTable } from "./Utility";
import { GetSalesViewurl } from "./Utility";
import { getCookie } from 'utility/utility';

const ReferralDataModal = (props) => {
  const { data, AgentInfo } = props;

  const handleSalesViewClick = async (row) => {
    try {
      const url = await GetSalesViewurl({
        Data: { 
          CustomerID: row.CustomerID, 
          BookingID: row.ReferralID 
        },
        ProductID: props.FilterProductId,
        IsMobileDevice: false,
        BookingType: 'Secondary',
        AgentInfo:  AgentInfo
      });
      
      if (url) {
        window.open(url, '_blank');
      }
    } catch (error) {
      console.error('Error opening sales view:', error);
    }
  };

  return (
    <>
      <Modal
        {...props}
        size="md"
        aria-labelledby="contained-modal-title-vcenter"
        centered
        fullscreen={false}
        onHide={props.onReferralModalCancel}
      >
        <Modal.Header closeButton>
          <Modal.Title id="contained-modal-title-vcenter">
            View Referrals
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Table responsive className="ReferralTable">
            <thead>
              <tr>
                {
                  ReferralTable && ReferralTable.length > 0 &&
                  ReferralTable.map((item, index) => {
                    return <th key={index}>{item.name}</th>
                  })
                }
              </tr>
            </thead>
            <tbody>
              {
                data && data.length > 0 &&
                data.map((row, serial) =>
                  ReferralTable && ReferralTable.length > 0 &&
                  <tr key={serial}>
                    {
                      ReferralTable.map((item, index) => {
                        const ModifiedColumnData = ModifyType(row[item.accessor], item.type);
                        if (index === 0) {
                          return <td key={index} data-title={item.name}>{serial + 1}</td>
                        }
                        if (item.accessor === 'ReferralID') {
                          return (
                            <td key={index} data-title={item.name}>
                              <a href="#" onClick={(e) => {
                                e.preventDefault();
                                handleSalesViewClick(row);
                              }}>
                                {ModifiedColumnData}
                              </a>
                            </td>
                          );
                        }
                        return <td key={index} data-title={item.name}>{ModifiedColumnData}</td>
                      })
                    }
                  </tr>
                )
              }
              { !(props.ReferralLoader) && data && data.length === 0 && 'No Referrals Found' }
              { props.ReferralLoader && data && data.length === 0 && <tr><td colSpan={ReferralTable.length}>Loading...</td></tr> }
            </tbody>
          </Table>
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={props.onReferralModalCancel}>Close</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
}

export default ReferralDataModal;