import React, { useState, useRef, useEffect } from "react";
import { Col, Row } from "reactstrap";
import { Button, Form } from "react-bootstrap";
import ManagerHierarchyV2 from "../Common/ManagerHierarchy_v2";
import { GetCommonspData, GetCommonspDataV2, GetCommonApiData, PostCommunicationData } from "../../store/actions/CommonAction";
import { GetCommonData } from "../../store/actions/CommonMongoAction";
import { connect } from 'react-redux';
import {
  ModifyType, DateRangeFilter, PerformActionsOnLinkClick, GetFirstDateOfMonth,
  InsertVerificationStatus, GetAgentProduct, FilterSearchedTextBookings, BmsDocPointUrl, GetBmsUrlStatus, GetVerificationStatusUrl,
   GetMyBookingsBmsApi2,
  GetSalesViewurl, ProductMap, GetHashUrlParameters, GetContinureJourneyUrl, GetTableColumnsInfo,
  AL<PERSON>, ALL_AGENTS, SECONDARY, ALL_BOOKINGS_TEXT, SECONDARY_BOOKINGS_TEXT, NumberOfPages,HIERARCHY_MESSAGE_NOTE, GetOptForWA, GetBookingById,
  GetBadgeCounterFromBookings_V2
} from "./Utility";
import _ from 'underscore';
import ShimmerLoader from "./ShimmerLoader";
import NavbarMobileView from "./NavBarMobileView";
import SideBarMobileView from "./SideBarMobileView";
import ViewRemarksModal from "./ViewRemarksModal";
import AddRemarksModal from "./AddRemarksModal";
import AddVerificationRemark from "./AddVerificationRemark";
import VerificationComment from "./VerificationComment";
import Pagination from "./Pagination";
import Messages from "./Messages";
import OpenWebUrlModal from "./OpenWebUrlModal"
import { getuser, IsMobile } from "../../utility/utility";
import { toast, ToastContainer } from 'react-toastify';
import TableDataFormat from "./TableDataFormat";
import FullScreenNewTab from "../../views/Common/FullScreenNewTab";
import ExportExcel from "../../views/Common/ExcelExport";
import { MY_BOOKINGS } from "../../variables/constants";
import moment from "moment";
import AutoPayStatusModal_V2 from "./AutoPayStatusModal_V2";
import AutoDebitPrompt from "./AutoDebitPrompt";
import {gaEventTracker, getUserDetails} from '../../utility/utility';
import ReferralDataModal from "./ReferralDataModal";

const MyBookings_V2 = (props) => {

  let DateValue = GetFirstDateOfMonth();
  const [MySpanBookings, setMySpanBookings] = useState([]);
  const [MySpanBookingsClone, setMySpanBookingsClone] = useState([]);
  const [AllBookingsCount, setAllBookingsCount] = useState(0);
  const [BookingsMap, setBookingsMap] = useState([]);
  const [BookingsMapAgentWise, setBookingsMapAgentWise] = useState({});
 // const [BookingsMapAgentWiseClone, setBookingsMapAgentWiseClone] = useState({});
  const [BadgeCounter, setBadgeCounter] = useState([]);
  const [AllBookingsPrimary, setAllBookingsPrimary] = useState([]);
  const [AllBookingsSecondary, setAllBookingsSecondary] = useState([]);
  const [AllBookingsCountSecondary, setAllBookingsCountSecondary] = useState(0);
  const [BookingsMapSecondary, setBookingsMapSecondary] = useState([]);
  const [BookingsMapSecondaryAgentWise, setBookingsMapSecondaryAgentWise] = useState({});
  //const [BookingsMapSecondaryAgentWiseClone, setBookingsMapSecondaryAgentWiseClone] = useState({});
  const [BadgeCounterSecondary, setBadgeCounterSecondary] = useState([]);
  const [SearchText, setSearchText] = useState("");
  const [DebouncedSearchText, setDebouncedSearchText] = useState("");
  const [MyAgents, setMyAgents] = useState([]);
  const [ActiveButtonIds, setActiveButtonIds] = useState([ALL]);
  const [CurrentPage, setCurrentPage] = useState(1);
  const [RowsPerPage, setRowsPerPage] = useState(10);
  const [itemOffset, setItemOffset] = useState(0);
  const [SelectedAgent, setSelectedAgent] = useState(null);
  const [SearchByField, setSearchByField] = useState(0);
  const [BookingMonth, setBookingMonth] = useState(DateValue);
  const [IsLoading, setIsLoading] = useState(true);
  const [ActiveTabParent, setActiveTabParent] = useState('Primary');
  const [ViewRemarksData, setViewRemarksData] = useState([]);
  const [AddRemarksData, setAddRemarksData] = useState("");
  const [AddResolvedData, setAddResolvedData] = useState("");
  const [CurrentRow, setCurrentRow] = useState("");
  const [ViewModalShow, setViewModalShow] = useState(false);
  const [AddModalShow, setAddModalShow] = useState(false);
  const [ResolvedShow, setResolvedShow] = useState(false);
  const [VerificationModalShow, setVerificationModalShow] = useState(false);
  const [OpenWebUrlModalShow, setOpenWebUrlModalShow] = useState(false);
  const [AutoPayModalShow, setAutoPayModalShow] = useState(false);
  const [ToggleSearchDate, setToggleSearchDate] = useState(false);
  const [FilterProductId, setFilterProductId] = useState(null);
  const [ProductIds, setProductIds] = useState("");
  const [ProductDetails, setProductDetails] = useState("");
  const [ProductsList, setProductsList] = useState([]);
  const [IsProductPresent, setIsProductPresent] = useState(null);
  const [OpenedLead, setOpenedLead] = useState(null);
  const [BookingDescription, setBookingDescription] = useState(ALL_BOOKINGS_TEXT);
  const [WebUrl, setWebUrl] = useState(null);
  const [WebUrlTitle, setWebUrlTitle] = useState("");
  const [ManagerIds, setManagerIds] = useState('');
  const [AgentInfo, setAgentInfo] = useState('{}');
  const [IsMobileDevice, setIsMobileDevice] = useState(null);
  const IsInitialMount = useRef(true);
  const IsSupervisor = useRef(false);
  const [refreshFilter, setRefreshFilter] = useState(false);
  const [ReferralModalShow, setReferralModalShow] = useState(false);
  const [ReferralData, setReferralData] = useState([]);
  const [ReferralLoader, setReferralLoader] = useState(false);

  const [tableDataStates, setTableDataStates] = useState({
    verificationQueueInfo: null
  });
  const invisibleButton= useRef(null);
  const [invisibleButtonState, setInvisibleButtonState]= useState({
    invisibility:false,
    invisibleButtonId:""
  });
  const [refresh, setRefresh]= useState(false);
  const [fetchDetailsByBookingId, setFetchDetailsByBookingId ] = useState(false);

  const [autoDebitPrompt, setAutoDebitPrompt]= useState({
    data: null,
    openPrompt: false
  })
  const [IsFosSMEAgent,setIsFosSMEAgent] = useState(false);

  let [TotalAPE, setTotalAPE] = useState(0);

  let userDetails = useRef(null);

  const user = getuser();   
  
  const RoleId = user.RoleId;
  const ProcessID = user.ProcessID;

  const endOffset = itemOffset + RowsPerPage;
  let currentItems = Array.isArray(MySpanBookings) && MySpanBookings.slice(itemOffset, endOffset);
  currentItems = currentItems && currentItems.length > 0 && currentItems.sort((a, b) => a.Id - b.Id);
  const pageCount = (Array.isArray(MySpanBookings) && Math.ceil(MySpanBookings.length / RowsPerPage)) || 0;
  let SERIAL = itemOffset + 1;

  const UpdateSearchFilterUrl = (product, month) => {
    localStorage.setItem('MyBookingsProduct', product);
    localStorage.setItem('MyBookingsMonth', month);
  }

  const handleProductChange = (e) => {
    UpdateSearchFilterUrl(e.target.value, BookingMonth);
    const ProductInfo = JSON.parse(e.target.value);
    let user= getuser();
    setProductDetails(e.target.value);
    setProductIds(ProductInfo.Id || '');
  
    if(user?.ProcessID===10 && ProductInfo.Id=="131")
    {
      setFilterProductId(131 || null);
    }
    else{
    setFilterProductId(ProductInfo.UIStructure || null);
    }
    setIsProductPresent(parseInt(ProductInfo.Present) || 0);
  }
  const handleBookingMonthChange = (e) => {
    UpdateSearchFilterUrl(ProductDetails, e.target.value);
    setBookingMonth(e.target.value)
  }

  const handleBookingMonthMobile = (month, product) => {
    UpdateSearchFilterUrl(product, month);
    setBookingMonth(month);
  }

  const handleProductChangeMobile = (month, product) => {
    UpdateSearchFilterUrl(product, month);
    setProductDetails(product);
    let user =getuser();
    const ProductInfo = JSON.parse(product);
    setProductIds(ProductInfo.Id || '');
    if(user?.ProcessID==10 && ProductInfo.Id=='131')
    {
      setFilterProductId(131 || null)
    }
    else{
      setFilterProductId(ProductInfo.UIStructure || null);
    }
   
    setIsProductPresent(parseInt(ProductInfo.Present) || 0)
  }

  const handlePageClick = (event) => {
    const newOffset = (event.selected * RowsPerPage) % MySpanBookings.length;
    setItemOffset(newOffset);
  };

  const handleShow = (e) => {
    if(e.FetchedViaClick) {
      const Managers = e.SelectedSupervisors?.join(',') || '';
      setManagerIds(Managers);
    }
  }

  const handleSelectedAgent = (e) => {
    setSelectedAgent(e.target.value);
   
    if(ActiveTabParent=='Primary')
    {
    setActiveButtonIds([ALL])
    }
    else{
      setActiveButtonIds([SECONDARY])
    }
  }

  const handleSearchBy = (e) => {
    setSearchByField(e.target.value);
  }

  const filterBookings = ({ activeIds }) => {
    let bookings = [];

    if (activeIds.includes(ALL)) {
      setActiveTabParent('Primary')

      if(SelectedAgent=="ALL_AGENTS")
      {
      bookings= AllBookingsPrimary || [];
      }
      else{
        bookings= BookingsMapAgentWise[SelectedAgent] || []
      }
      // for (let key in BookingsMap) {
      //   bookings = [...bookings, ...BookingsMap[key]];
      // }
    } else if (activeIds.includes(SECONDARY)) {
      setActiveTabParent('Secondary')

      if(SelectedAgent == "ALL_AGENTS")
      {
      bookings = AllBookingsSecondary || [];
      }
      else{
        bookings= BookingsMapSecondaryAgentWise[SelectedAgent] || []
      }
      // for (let key in BookingsMapSecondary) {
      //   bookings = [...bookings, ...BookingsMapSecondary[key]];
      // }
    } else {
      if (ActiveTabParent === 'Primary') {
       
        // if(activeIds.includes(ALL))
        // {
      
        // bookings= BookingsMapAgentWise[SelectedAgent] || []
        // }
        // else{
        bookings= BookingsMap[parseInt(activeIds[0])] || []
        // }
        // for (let key in BookingsMap) {
        //   // const arr = key;
        //   const id = (Array.isArray(activeIds) &&  activeIds[0]) || 0;
       
        //   if (key==id) {
        //     bookings = [...bookings, ...BookingsMap[key]];
        //   }
        // }
      } else {
        // if(activeIds.includes(SECONDARY))
        // {
        //   bookings= BookingsMapSecondaryAgentWise[SelectedAgent] || []
        // }
        // else{
        bookings=BookingsMapSecondary[parseInt(activeIds[0])] || [];
        // }
        // for (let key in BookingsMapSecondary) {
        //   // const arr = JSON.parse(key);
        //   const id = (Array.isArray(activeIds) &&  activeIds[0]) || 0;
        //   if (key==id) {
        //     bookings = [...bookings, ...BookingsMapSecondary[key]];
        //   }
        // }
      }
    
    }

    bookings =Array.isArray(bookings) && bookings.sort((a, b) => new Date(b.BookingDate) - new Date(a.BookingDate));

    if(Array.isArray(activeIds) && activeIds.includes(17))
    {
      bookings = Array.isArray(bookings) && bookings.sort((a,b)=> new Date(b.ClaimAmount) - new Date(a.ClaimAmount));
    }
   
    setMySpanBookings(bookings);
    setMySpanBookingsClone(bookings);
  }

  const toggleNavButtons = (id, description) => {
    const index = ActiveButtonIds.indexOf(id);
    
    if(id==17)
    {
      gaEventTracker('MyBookings','ClaimTakenFilter',userDetails.current)
    }
    
    setItemOffset(0);
    // setSearchText("");
    setBookingDescription(description)
    if (index > -1) {
    } else {

      setActiveButtonIds([id])
      filterBookings({ activeIds: [id] });

      // filter data according to entered searchtext
      // if([ALL, SECONDARY].includes(id) ){
        
      //   setRefreshFilter(!refreshFilter);
      // }
    }
  }


  const handleRowsCountChange = (e) => {
    const element = document.getElementById(`tr-${e.target.value - 1}`);
    element && element.scrollIntoView();
    setRowsPerPage(parseInt(e.target.value));
  }

  const handleChangeDebounceSearchText = (e) => {
    let text = e.target.value;
    text = text.trim();
    setDebouncedSearchText(text && text.toLowerCase())
  }

  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchText(DebouncedSearchText)
    }, 500)

    return () => clearTimeout(timer)
  }, [DebouncedSearchText])

  useEffect(() => {
    let TotalAPEFiltered = 0;
    for(let item in MySpanBookings)
      {
        TotalAPEFiltered = TotalAPEFiltered + MySpanBookings[item].APE;
      }
    let value = new Intl.NumberFormat('en-IN', {}).format(TotalAPEFiltered)
    setTotalAPE(value);
  },[MySpanBookings])

  useEffect(() => {

    if (SearchText && SearchText.length > 0) {
      const FilteredBookings = FilterSearchedTextBookings({ Bookings: MySpanBookingsClone, TextToSearch: SearchText })
      setItemOffset(0);
      setMySpanBookings(FilteredBookings);

    } else {
      filterBookings({ activeIds: ActiveButtonIds });
      const SliderButtons = ActiveButtonIds.filter(item => item !== ALL && item !== SECONDARY)
      if (ToggleSearchDate && SliderButtons && SliderButtons.length === 0) {
        if (ActiveTabParent === 'Primary') {
          setMySpanBookings(AllBookingsPrimary);
          setMySpanBookingsClone(AllBookingsPrimary)
        } else if (ActiveTabParent === 'Secondary') {
          setMySpanBookings(AllBookingsSecondary);
          setMySpanBookingsClone(AllBookingsSecondary)
        } else {
          filterBookings({ activeIds: ActiveButtonIds });
        }
      }
    }
  }, [SearchText, refreshFilter]);

  const ClearPrimaryBookingsState = () => {
    setBadgeCounter([]);
    setAllBookingsCount(0);
    setMySpanBookings([]);
    setMySpanBookingsClone([]);
    setAllBookingsPrimary([]);
    setBookingsMap({});
    setBookingsMapAgentWise({});
  }

  const ClearSecondaryBookingsState = () => {
    setAllBookingsSecondary([]);
    setBadgeCounterSecondary([]);
    setAllBookingsCountSecondary(0);
    setBookingsMapSecondary({});
    setBookingsMapSecondaryAgentWise({});
  }

  const SetPrimaryBookingsData = (PrimaryBookings) => {
    if (PrimaryBookings) {
      let {BookingDetailsPrimaryList, BookingStatusTypeList, AgentList}= PrimaryBookings?.data || {};
      const UserInfo = PrimaryBookings?.info || null;
     
      let BookingsMapAgentWise = _.groupBy(BookingDetailsPrimaryList, 'EmployeeId')
      setMyAgents(AgentList);
      BookingStatusTypeList = Array.isArray(BookingStatusTypeList) && BookingStatusTypeList.filter(item => item.BookingStatusType != 0);
      setBadgeCounter(BookingStatusTypeList);
      setMySpanBookings(BookingDetailsPrimaryList);
      setMySpanBookingsClone(BookingDetailsPrimaryList);
      setIsLoading(false);
      setToggleSearchDate(true);
      setAllBookingsPrimary(BookingDetailsPrimaryList);
      setAllBookingsCount(BookingDetailsPrimaryList?.length || 0);

      const obj = {};

      if (Array.isArray(BookingDetailsPrimaryList)) {
        BookingDetailsPrimaryList.forEach((Booking) => {
          const statusTypes = Booking['BookingStatusType'];

          if (Array.isArray(statusTypes)) {
            statusTypes.forEach((status) => {
              if (!obj[status]) {
                obj[status] = [];
              }
              obj[status].push(Booking);
            });
          }
        });
      }

      setBookingsMap(obj);

      setBookingsMapAgentWise(BookingsMapAgentWise);
      setSelectedAgent(ALL_AGENTS);
      if(UserInfo) {
        setAgentInfo(window.atob(UserInfo) || '{}');
      }
     
    } else {
      setIsLoading(false);
      ClearPrimaryBookingsState();
    }
  }

  const SetSecondaryBookingsData = (SecondaryBookings) => {
    if (SecondaryBookings) {
      let {BookingDetailsPrimaryList, BookingStatusTypeList, AgentList}= SecondaryBookings?.data || {};
      let BookingsMapAgentWise = _.groupBy(BookingDetailsPrimaryList, 'EmployeeId')
      BookingStatusTypeList = Array.isArray(BookingStatusTypeList) && BookingStatusTypeList.filter(item => item.BookingStatusType != 0);

      setBadgeCounterSecondary(BookingStatusTypeList);
      setAllBookingsSecondary(BookingDetailsPrimaryList)
      setAllBookingsCountSecondary(BookingDetailsPrimaryList?.length || 0)


      let secondaryMap={};
     

      if (Array.isArray(BookingDetailsPrimaryList)) {
        BookingDetailsPrimaryList.forEach((Booking) => {
          const statusTypes = Booking['BookingStatusType'];
      
          if (Array.isArray(statusTypes)) {
            statusTypes.forEach((status) => {
              if (!secondaryMap[status]) {
                secondaryMap[status] = []; 
              }
              secondaryMap[status].push(Booking);
            });
          }
        });
      }
      
      setBookingsMapSecondary(secondaryMap);

      // setBookingsMapSecondary(_.groupBy(BookingDetailsPrimaryList, 'BookingStatusType'));
      setBookingsMapSecondaryAgentWise(BookingsMapAgentWise);

    } else {
      ClearSecondaryBookingsState();
    }
  }

  const getBookingDetailsById = async () => {
    setFetchDetailsByBookingId(true);

    const SearchText = DebouncedSearchText;


    let BookingType=1;
    if(ActiveTabParent=='Secondary')
    {
      
        BookingType=2;
    }
    let BookingDetails = await GetBookingById({ SearchText: SearchText, SearchByField: SearchByField, BookingType: BookingType });
    
    setItemOffset(0);
  
    if(BookingDetails && !BookingDetails.isError && BookingDetails.data && BookingDetails.data.BookingDetailsPrimaryList){
      if(ProductsList && ProductsList.length > 0 && BookingDetails.data.BookingDetailsPrimaryList?.[0]?.ProductID){
        const productDetails = ProductsList.find((item) => {
          if(item.Id !== ""){
            let itemIdsArray = item.Id.split(","); 
            return itemIdsArray.includes((BookingDetails.data.BookingDetailsPrimaryList[0].ProductID).toString())
          }
          return false;
        })
        if(productDetails){
          const e = { target: { value: JSON.stringify(productDetails) }}
          handleProductChange(e);
        }
      }
  
      if(BookingDetails.data.BookingDetailsPrimaryList?.[0]?.BookingDate){
        const bookingDate = GetFirstDateOfMonth(new Date(BookingDetails.data.BookingDetailsPrimaryList[0].BookingDate));
        const e = { target: { value: bookingDate }}
        handleBookingMonthChange(e);
      }
  
      if(ActiveTabParent=='Secondary')
      {
        SetSecondaryBookingsData(BookingDetails);
        ClearPrimaryBookingsState();
      }
      else{
      SetPrimaryBookingsData(BookingDetails);
      ClearSecondaryBookingsState();
      }
    } else {
      if(BookingDetails.isError){
        toast(BookingDetails.errorMessage, { type: 'error' });
      } else if(BookingDetails.data?.BookingDetailsPrimaryList == null){
        toast("Something went wrong", { type: "error" });
      }
    }

  }

  const handleDateRangeChangeMethod = async ({BookingMonth, RoleId}) => {
    setIsLoading(true);
    const Offset = (CurrentPage - 1) * RowsPerPage;
    setItemOffset(Offset);
    setSearchText("");
    setDebouncedSearchText("");
    const ids = ActiveButtonIds.filter(item => item === ALL || item === SECONDARY);
    setActiveButtonIds(ids);
    ClearPrimaryBookingsState();
    ClearSecondaryBookingsState();

    let InitialDate= new Date(BookingMonth.replace(/-/g, "/"));
    let nextMonthDate = new Date(InitialDate);
    nextMonthDate.setMonth(InitialDate.getMonth() + 1);
      
    // let ToDate = nextMonthDate.toLocaleDateString();
    let FromDate =moment(InitialDate).format("YYYY-MM-DD");
    let ToDate = moment(nextMonthDate).format("YYYY-MM-DD");

    

    if (!ProductIds || (RoleId && ![12, 13].includes(RoleId) && !ManagerIds)) {
      setIsLoading(false);
    } else {
      // let PrimaryResponse = await GetMyBookingsBmsApi({ BookingType: 1, ProductIds: ProductIds, FilterProduct: FilterProductId, BookingMonth, ManagerIds: ManagerIds, ProductPresent: IsProductPresent, IsSupervisor: IsSupervisor.current });

      
      let PrimaryResponse = await GetMyBookingsBmsApi2({ BookingType: 1, ProductIds: ProductIds, FromDate: FromDate, ToDate: ToDate  ,ManagerIds: ManagerIds, ProductPresent: IsProductPresent, Isadvisor: !IsSupervisor.current, IsSupervisor: IsSupervisor.current });
      
      // let SecondaryResponse = await GetMyBookingsBmsApi({ BookingType: 2, ProductIds: ProductIds, FilterProduct: FilterProductId, BookingMonth, ManagerIds: ManagerIds, ProductPresent: IsProductPresent, IsSupervisor: IsSupervisor.current });
      let SecondaryResponse = await GetMyBookingsBmsApi2({ BookingType: 2, ProductIds: ProductIds,  FromDate: FromDate, ToDate: ToDate, ManagerIds: ManagerIds, ProductPresent: IsProductPresent, Isadvisor: !IsSupervisor.current, IsSupervisor: IsSupervisor.current });

      SetPrimaryBookingsData(PrimaryResponse);
      SetSecondaryBookingsData(SecondaryResponse);
      
      setRefreshFilter(!refreshFilter);

      setIsLoading(false);
    }
  }

  useEffect(() => {
    const FetchBookingsData = async () => {
      
      const { RoleId } = getuser();

      // if ([12].includes(RoleId)) {
      //   IsSupervisor.current = true;
      // }

      if(RoleId!=13)
      {
        IsSupervisor.current = true
      }

      if (ActiveTabParent === 'Primary') {
        setBookingDescription(ALL_BOOKINGS_TEXT);
      } else {
        setBookingDescription(SECONDARY_BOOKINGS_TEXT);
      }

      if(!fetchDetailsByBookingId){
        if (IsInitialMount.current) {
          handleDateRangeChangeMethod({ BookingMonth, RoleId });
          IsInitialMount.current = false;
        } else {
          if (ProductIds === '') {
            setAllBookingsCount(0);
            setAllBookingsCountSecondary(0);
            toast("Please select your Product", { type: 'error' });
          } else if (RoleId && (!([12, 13].includes(RoleId))) && !ManagerIds) {
            // toast("Please select Managers and Supervisors from the show hierarchy button", { type: 'error' });
          } else {
            handleDateRangeChangeMethod({ BookingMonth, RoleId });
          }
        }
      }

      setFetchDetailsByBookingId(false);
    }
    FetchBookingsData()
  }, [BookingMonth, ProductIds, ManagerIds, refresh]);

  useEffect(() => {
    const MapBookingsData = async () => {
      let bookingsPrimary = [];
      let bookingsSecondary = [];
      if (SelectedAgent === ALL_AGENTS) {
        // if (ActiveTabParent === 'Primary') {
          for (let key in BookingsMapAgentWise) {
            bookingsPrimary = [...bookingsPrimary, ...BookingsMapAgentWise[key]];
          }
        // } else {
          for (let key in BookingsMapSecondaryAgentWise) {
            bookingsSecondary = [...bookingsSecondary, ...BookingsMapSecondaryAgentWise[key]];
          }
        // }
      } else {
        bookingsPrimary = BookingsMapAgentWise[SelectedAgent] || [] ;
        bookingsSecondary = BookingsMapSecondaryAgentWise[SelectedAgent] || [];
        // bookings = ActiveTabParent === 'Primary' ? BookingsMapAgentWise[SelectedAgent] || [] : BookingsMapSecondaryAgentWise[SelectedAgent] || []
      }
      let UpdatedBadgeCounter = GetBadgeCounterFromBookings_V2(bookingsPrimary);
      let UpdatedSecondaryBadgeCounter = GetBadgeCounterFromBookings_V2(bookingsSecondary);

      let primaryMap={};

      if (Array.isArray(bookingsPrimary)) {
        bookingsPrimary.forEach((Booking) => {
          const statusTypes = Booking['BookingStatusType'];

          if (Array.isArray(statusTypes)) {
            statusTypes.forEach((status) => {
              if (!primaryMap[status]) {
                primaryMap[status] = []; 
              }
              primaryMap[status].push(Booking); 
            });
          }
        });
      }

      let secondaryMap={};

      if (Array.isArray(bookingsSecondary)) {
        bookingsSecondary.forEach((Booking) => {
          const statusTypes = Booking['BookingStatusType'];

          if (Array.isArray(statusTypes)) {
            statusTypes.forEach((status) => {
              if (!secondaryMap[status]) {
                secondaryMap[status] = []; 
              }
              secondaryMap[status].push(Booking);
            });
          }
        });
      }

      bookingsPrimary=Array.isArray(bookingsPrimary) && bookingsPrimary.sort((a, b) => new Date(b.BookingDate) - new Date(a.BookingDate));
      bookingsSecondary=Array.isArray(bookingsSecondary) && bookingsSecondary.sort((a, b) => new Date(b.BookingDate) - new Date(a.BookingDate));
      // if (ActiveTabParent === 'Primary') {
        setBookingsMap(primaryMap);
        setBadgeCounter(UpdatedBadgeCounter);
        setAllBookingsCount(bookingsPrimary.length || 0);
      // } else {
        setBookingsMapSecondary(secondaryMap);
        setBadgeCounterSecondary(UpdatedSecondaryBadgeCounter);
        setAllBookingsCountSecondary(bookingsSecondary.length || 0);
      // }
      if(ActiveTabParent === "Primary")
      {
      setMySpanBookings(bookingsPrimary);
      }
      else{
        setMySpanBookings(bookingsSecondary);
      }
    }
    MapBookingsData()
  }, [SelectedAgent, BookingsMapAgentWise, BookingsMapSecondaryAgentWise, ActiveTabParent]);

  useEffect(() => {
    const SetProductDetails = async () => {
      const type = IsMobile();
      setIsMobileDevice(type);
     
      GetAgentProduct(props, function (data) {
        if(parseInt(user?.ProcessID)==10 && Array.isArray(data?.data) && data.data.length>0)
        {
             data.data.filter((data)=>{
              if(data.Id=='131')
              {
                data.UIStructure=131;
              }
             })
        }
        setProductsList(data?.data || []);
        
        
      });
  
      const { Product, Month } = GetHashUrlParameters();
      const { Id, UIStructure, Present } = (Product && JSON.parse(Product)) || {};
      setProductDetails(Product)
      setProductIds(Id);
      setIsProductPresent(Present);
      setFilterProductId(UIStructure);
      setBookingMonth(Month);
    }
    SetProductDetails()

    let userId = getUserDetails('UserId');
    userDetails.current = parseInt(userId);
    
  }, []);

  const handleLinkClick = async (e, data, col) => {
    e.preventDefault();
    const { ErrorStatus, Data, AdditionalInfo } = await PerformActionsOnLinkClick({ data, col, FilterProductId });
      if (ErrorStatus) {
        toast(Data, { type: 'error' });
      } else {
        toast(Data, { type: 'success' });
        let verificationQueueInfo = AdditionalInfo.verificationQueueInfo;
        setTableDataStates((state) => ({
          ...state,
          verificationQueueInfo: verificationQueueInfo
        }))
      }
  }

  const handlePopUp = async (e, data, col) => {
    setCurrentRow(data);
    e.preventDefault();

    if (col.name === 'ViewRemarks') {
      setViewModalShow(true);
      props.GetCommonspDataV2({
        root: 'GetLeadRemarks',
        c: "R",
        params: [{ BookingID: data.BookingID }],

      }, function (data) {
        if (data?.data?.data && data.data.data.length > 0) {
          setViewRemarksData(data.data.data[0]);
        }
      })
    } else if (col.name === 'Verification Comment') {
      setVerificationModalShow(true)
    } else if (col.name === 'AddRemarks') {
      setAddModalShow(true);
      setAddRemarksData(true);
    } else if (col.name === 'AutoDebitStatus') {
      setAutoPayModalShow(true);
    }
    else if (col.name === 'Verification Remark') {
      setResolvedShow(true);
      setAddResolvedData(true);
    } else if (col.name === 'Upload Docs') {
      debugger
      let url = await BmsDocPointUrl(data);
      if (url) {
        if (!IsMobileDevice) {
          window.open(url);
        } else {
          window.open(url, '_self');
        }
      } else {
        toast("Error occured: Unable to open", { type: 'error' });
      }
    } else if (col.name === 'BookingID') {
      let url = await GetSalesViewurl({ Data: data, IsMobileDevice, ProductID: data?.ProductID, BookingType: ActiveTabParent, AgentInfo });

      if (url) {
        if (!IsMobileDevice) {
          window.open(url);
        } else {
          window.open(url, '_self');
        }
      } else {
        toast("Error occured: Unable to open", { type: 'error' });
      }
    }
    else if (col.name === 'ContinueJourney') {
      let url = await GetContinureJourneyUrl({ Data: data});
      if (url) {
        if (!IsMobileDevice) {
          window.open(url);
        } else {
          window.open(url, '_self');
        }
      } else {
        toast("Error occured: Unable to open", { type: 'error' });
      }
    }
    else if (col.name === 'Status') {
      let url = await GetBmsUrlStatus({ Data: data});
      if (url) {
        if (!IsMobileDevice) {
          window.open(url);
        } else {
          window.open(url, '_self');
        }
      }  else {
        toast("Error occured: Unable to open", { type: 'error' });
      }
    } else if (col.name === 'VerificationStatus') {
      let url = await GetVerificationStatusUrl({ FilterProductId, Data: data });
      if (url) {
        if (!IsMobileDevice) {
          window.open(url);
        } else {
          window.open(url, '_self');
        }
      }else {
        toast("Error occured: Unable to open", { type: 'error' });
      }
    }
    else if(col.name ==='AddMom')
    {
      handleOpenAddMomModal(data?.CustomerID, data.BookingID);
    }
    else if(col.name ==='ViewMom')
    {
        handleOpenViewMomModal(data?.CustomerID, data.BookingID);
    }
    else if (col.name === 'ReferralInfo') {
      setReferralModalShow(true);
      setReferralLoader(true);
      props.GetCommonspDataV2({
        root: 'GetReferralsAndBookingStatus',
        c: "R",
        params: [{ LeadID: data.BookingID }],

      }, function (data) {
        setReferralLoader(false);
        if (data?.data?.data && data.data.data.length > 0) {
          setReferralData(data.data.data[0]);
        }
      })
    }
  }

  const handleRemarkInputChange = (e) => {
    setAddRemarksData(e.target.value);
  }

  const handleResolvedInputChange = (e) => {
    setAddResolvedData(e.target.value);
  }

  const handleRemarkSave = () => {
    setAddModalShow(false);
    props.GetCommonspDataV2({
      root: 'SaveLeadHistory',
      c: "L",
      params: [{ "LeadID": CurrentRow.BookingID, "StatusId": CurrentRow.StatusID, "SubstatusId": CurrentRow.SubStatusID, "EventType": 7, "Comments": AddRemarksData, "Type": 0, }],
    }, function (result) {
    })
  }

  const handleResolvedSave = async () => {
    let body = {
      ...CurrentRow,
      Remarks: AddResolvedData,
      VerificationStatus: 'Pending',
      VerificationSubStatus: 'Solved by Agent'
    };
    setResolvedShow(false);
    let response = await InsertVerificationStatus(body);
    if (response) {
      toast(`Verification remark added successfully on BookingId: ${CurrentRow.BookingID}`, { type: 'success' });
      window.location.reload();
    } else {
      toast("Failed to add verification remark", { type: 'error' });
    }
  }

  const handleUpdateAutoPayData = ({ BookingID, PrevStatus, CurrentStatus }) => {
    if (PrevStatus !== CurrentStatus) {
      const index = MySpanBookings && MySpanBookings.length > 0 && MySpanBookings.map(item => item.BookingID).indexOf(BookingID);
      const UpdatedBooking = { ...MySpanBookings[index], IsSI: CurrentStatus };
      const Bookings = [...MySpanBookings.slice(0, index), UpdatedBooking, ...MySpanBookings.slice(index + 1)];
      setMySpanBookings(Bookings);
      setMySpanBookingsClone(Bookings);
    }
  }

  const handleOpenAddMomModal = (customerId, parentId) => {
    try {
      const baseUrl = document.referrer; // Extract base URL
      if (baseUrl != "") {
        const url = `${baseUrl}sme/MoM/${customerId}/${parentId}/true`; // Construct the full URL
        setWebUrl(url);
        setOpenWebUrlModalShow(true);
      }
      else {
        toast.error("Url not found, please try again later");
      }

    }
    catch {
      toast.error("Url not found, please try again later");
    }

  };

  const handleOpenViewMomModal = (customerId, parentId) => {
    try {
      const baseUrl = document.referrer; // Extract base URL
      if (baseUrl != "") {
        const url = `${baseUrl}sme/ViewMom/${customerId}/${parentId}/true`; // Construct the full URL
        setWebUrl(url);
        setOpenWebUrlModalShow(true);
      }
      else {
        toast.error("Url not found, please try again later");
      }

    }
    catch {
      toast.error("Url not found, please try again later");
    }
  };


  useEffect(()=>{
    if(invisibleButtonState?.invisibleButtonId && invisibleButtonState.invisibleButtonId.length>0)
    {
      invisibleButton.current.click();
    }
  },[invisibleButtonState])
  useEffect(() => {
      IsSMEFosAgent();
  }, [FilterProductId])


  const IsSMEFosAgent = () => {
    try {
      props.GetCommonspDataV2({
        root: 'GetGroupsByUserId',
        c: "R",
        params: [{}],
      }, (groupData) => {
        if (groupData && Array.isArray(groupData?.data?.data) && groupData.data.data.length > 0 && (getuser().RoleId === 13)) {
          let groupList = groupData.data.data[0];
          props.GetCommonData({
            root: 'SMEConfig',
            c: 'MATRIX_DASHBOARD_CLIENT',
          }, function ({ data }) {
            if (data && data.data && (data.data.length > 0)) {
              data.data.filter((d) => {
                if (d.key === 'SMEFosGroupIds') {
                  let smeFosGroups = d.data;
                  if (Array.isArray(smeFosGroups) && Array.isArray(groupList)) {
                    for (var i = 0, len = groupList.length; i < len; i++) {
                      if (smeFosGroups.indexOf(groupList[i].GroupId) > -1) {
                        setIsFosSMEAgent(true);
                        break;
                      }
                    }
                  }
                }
              })
            }
          })
        }
      }
      )
    }
    catch (e) {
      console.log(JSON.stringify(e));
    }
  };
  



  const handleOptWAClick=async (e,data)=>{
     e.preventDefault();
    // toast.error("Customer Has Not Opted for WA");
    const res=await GetOptForWA(data?.CustomerID); 
    if(res!=true)
    {
        toast.error("Customer "+data?.CustomerID+" has not opted for Whatsapp Communication");
    }
    else{
   
      setInvisibleButtonState({
        invisibility: !invisibleButtonState.invisibility,
        invisibleButtonId: `SME_WHATSAPP_MATRIXGO|${data?.BookingID}|${data?.CustomerID}|${data?.CustomerName}`

      })
   
      // invisibleButton.current.click();
    }
  }

  const handleAutoDebitPrompt=(e,data)=>{
        e.preventDefault();
        
        let Booking ={
          "LeadId":data.BookingID,
          "CustomerName":data?.CustomerName,
          "ProductId":data?.ProductID,
          "ProductName": data?.ProductName,
          "InsurerName":data?.Insurer,
          "PolicyNo":data?.PolicyNo,
          "ApplicationNo": data?.ApplicationNo
        }
        setAutoDebitPrompt({
          openPrompt: true,
          data: Booking
        })    
  }

  const HandleChangeOpenedLead = (e) => {
    debugger
    if (OpenedLead) {
      setOpenedLead(null);
      const id = e.target.id;
      const element = document.getElementById("tr-" + id.split('-')[1]);
      element.scrollIntoView();
    } else {
      setOpenedLead(parseInt(e.target.value || 0));
    }
  }

  const checkInvisbleButton=(e)=>{
    
    if(window.ReactNativeWebView){
      window.ReactNativeWebView.postMessage(e.target.id);
    }
    
  }

  const DateFilter = DateRangeFilter(FilterProductId);
  const MobileViewProps = {
    AllBookingsCount,
    AllBookingsCountSecondary,
    ActiveTabParent,
    BadgeCounter,
    BadgeCounterSecondary,
    FilterProductId
  }
  
  const { SliderFilters, TableColumnsProductWise } = GetTableColumnsInfo(FilterProductId,ActiveTabParent,IsFosSMEAgent,ActiveButtonIds)

  return (
    <div className="content MySpanBooking">
      <Row>
        <ToastContainer />
        <Col md={12}>
          <nav className="topbar-list">
            <div>
            
              <Col className="FirstCol">
              <h3 className="heading">My Bookings</h3>
                <>
                  <button
                    className={ActiveTabParent === 'Primary' ? "activeBtn" : ""}
                    onClick={() => toggleNavButtons(ALL, ALL_BOOKINGS_TEXT)}>
                    My Booking <span className="badge bg-primary">{AllBookingsCount}</span>
                  </button>
                  <button
                    className={ActiveTabParent === 'Secondary' ? "activeBtn" : ""}
                    onClick={() => toggleNavButtons(SECONDARY, SECONDARY_BOOKINGS_TEXT)}
                  >Secondary Booking
                    <span className="badge bg-primary">{AllBookingsCountSecondary}</span>
                  </button>
                  {/* <button
                    className={ActiveTabParent === 'Tertiary' ? "activeBtn" : ""}
                    onClick={() => openNewTabPaymentOverdue()}
                  >Payment Overdue Bookings
                    <span className="badge bg-primary">
                      
                      <img src={'/Images/newTab.png'} alt=""/>
                      </span>
                  </button> */}
                </>
                <div className="iconBtn">
                  {RoleId && ![13].includes(RoleId) && <FullScreenNewTab />}
                  {RoleId && ![13].includes(RoleId) && Array.isArray(currentItems) && currentItems.length > 0  && <ExportExcel data={currentItems} fileName={`MyBookings-${BookingMonth}`}/>}
                </div>
                {RoleId && ![13].includes(RoleId) &&
                    <Button className="refresh-btn" onClick={()=>setRefresh(!refresh)}>
                     <i className="fa fa-refresh"></i>
                    </Button>
                }
              </Col>
              
              </div>
              
              {/* <div className="col-right">
                 
              </div> */}
            </nav>
        </Col>

        <ViewRemarksModal
          show={ViewModalShow}
          onViewRemarksCancel={() => setViewModalShow(false)}
          data={ViewRemarksData}
        />
         <ReferralDataModal
          show={ReferralModalShow}
          onReferralModalCancel={() => setReferralModalShow(false)}
          data={ReferralData}
          ReferralLoader={ReferralLoader}
          FilterProductId={FilterProductId}
          AgentInfo={AgentInfo}
        />
        <AddRemarksModal
          show={AddModalShow}
          onAddRemarksCancel={() => setAddModalShow(false)}
          data={AddRemarksData}
          handleRemarkInputChange={handleRemarkInputChange}
          handleRemarkSave={handleRemarkSave}
        />
        <AddVerificationRemark
          show={ResolvedShow}
          onAddResolvedCancel={() => setResolvedShow(false)}
          data={AddResolvedData}
          handleResolvedInputChange={handleResolvedInputChange}
          handleResolvedSave={handleResolvedSave}
        />
        <AutoPayStatusModal_V2
          show={AutoPayModalShow}
          onAutoPayStatusCancel={() => setAutoPayModalShow(false)}
          handleUpdateAutoPayData={handleUpdateAutoPayData}
          current={CurrentRow}
        />

        <AutoDebitPrompt
        show = {autoDebitPrompt.openPrompt}
        onAutoDebitPromptClose = {()=>setAutoDebitPrompt({
          ...autoDebitPrompt,
          openPrompt: false
        })}
        booking={autoDebitPrompt.data || (() => {
          setAutoDebitPrompt({
            ...autoDebitPrompt,
            openPrompt: false
          });
          return null; 
        })}
        />

        

        <VerificationComment
          show={VerificationModalShow}
          onVerificationModalCancel={() => setVerificationModalShow(false)}
          current={CurrentRow}
        />
        <OpenWebUrlModal
          show={OpenWebUrlModalShow}
          onWebUrlModalCancel={() => setOpenWebUrlModalShow(false)}
          current={CurrentRow}
          webUrl={WebUrl}
          WebUrlTitle={WebUrlTitle}
          sme={IsFosSMEAgent}
        />

        <Col md={12}>
          <nav>
            {
              <Col className="secondCol">
                {
                  SliderFilters && SliderFilters.length > 0 && SliderFilters.map((item, index) => {
                    let active = ActiveButtonIds.includes(item.id) || false;
                    let defaultCss = "";
                    if (item.css) {
                      defaultCss = item.css
                    }
                    let statusType = item.BookingStatusType;
                    let BadgeCounterCurrent = ActiveTabParent === 'Primary' ? BadgeCounter : BadgeCounterSecondary;
                    let Badge = BadgeCounterCurrent && BadgeCounterCurrent.length > 0 && BadgeCounterCurrent.filter(item => item.BookingStatusType == statusType) || {};
                    let ActiveClass = item.activeClass || " activeBtn";
                    return <button key={index} className={active ? defaultCss + ActiveClass : defaultCss} onClick={() => toggleNavButtons(item.id, item.description)} > {item.text} {item.BookingStatusType != 100 && <span className="badge bg-primary">{Badge.length > 0 && Badge[0].Count || 0}</span>}</button>
                  })
                }
              </Col>
            }


          </nav>
          <NavbarMobileView
            handleChangeDebounceSearchText={handleChangeDebounceSearchText}
            value={DebouncedSearchText}
            toggleNavButtons={toggleNavButtons}
            MobileViewProps={MobileViewProps}
          />

          <SideBarMobileView
            handleBookingMonthMobile={handleBookingMonthMobile}
            handleProductChangeMobile={handleProductChangeMobile}
            ProductDetailsMobile={ProductDetails}
            ProductsList={ProductsList}
            BookingMonthMobile={BookingMonth}
          />

          <div className="SpanBookingDataTable">
            <div className="FilterSelection">

              <Row>
                <Col md={(RoleId && RoleId !== 13) ? 3 : 2}>
                  <label>Product</label>
                  <Form.Select aria-label="Default select" onChange={handleProductChange} value={ProductDetails}>
                    {
                      ProductsList && ProductsList.length > 0 &&
                      ProductsList.map((item, index) => {
                        return <option key={index} value={JSON.stringify(item)}>{item.Display}</option>
                      })
                    }
                  </Form.Select>
                </Col>
                <Col md={(RoleId && RoleId !== 13) ? 3 : 2}>
                  <label>Booking Month</label>
                  <Form.Select aria-label="Default select" onChange={handleBookingMonthChange} value={BookingMonth}>
                    {
                      DateFilter && DateFilter.length > 0 &&
                      DateFilter.map((item, index) => {
                        return <option key={index} value={item.value}>{item.month + "-" + item.year}</option>
                      })
                    }
                  </Form.Select>
                </Col>
                {RoleId && RoleId != 13 &&
                  <Col md={3}>
                    <label>Select Agent</label>
                    <Form.Select value={SelectedAgent} aria-label="Default select" onChange={handleSelectedAgent}>
                      <option value={ALL_AGENTS}>All Agents</option>
                      {
                        MyAgents && MyAgents.length > 0 &&
                        MyAgents.map((item, index) => {
                          return <option
                            key={index}
                            value={item.EmployeeId}
                          >
                            {item.EmployeeId + " " + item.UserName}
                          </option>
                        })
                      }
                    </Form.Select>
                  </Col>
                }
                {<Col md={(RoleId && RoleId !== 13) ? 3 : 2}>
                  <label>Total APE</label>
                  <div className="total-ape">
                    <label>{TotalAPE}</label>
                  </div>
                </Col>}
                {RoleId && RoleId === 13 && <Col md={3}>
                  <label>Search</label>
                  <div className="form-group has-search">
                    <span className="fa fa-search form-control-feedback"></span>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Search BookingID, Application No …"
                      onChange={handleChangeDebounceSearchText}
                      value={DebouncedSearchText}
                    />
                  </div>
                </Col>}

              </Row>
              <Row>
               {RoleId && RoleId != 13 && 
              //  ActiveTabParent === 'Primary' &&
               <>
               <Col md={3}>
                    <label>Select Search By</label>
                    <Form.Select aria-label="Default select" onChange={handleSearchBy}>
                      <option value={0}>Select</option>
                      <option value={1}>ApplicationNo</option>
                      <option value={2}>BookingID</option>
                    </Form.Select>
                  </Col>
                
                <Col md={3}>
                  <label>Search</label>
                  <div className="form-group has-search">
                    <span className="fa fa-search form-control-feedback"></span>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Search BookingID, Application No …"
                      onChange={handleChangeDebounceSearchText}
                      value={DebouncedSearchText}
                    />
                  </div>
                </Col>

                  <Col md={1} className="text-center mt-4">
                  <Button  className="SearchBtn"
                    disabled = { (!(DebouncedSearchText &&  DebouncedSearchText.length >= 8)) || (parseInt(SearchByField) === 0) }
                    onClick = { () => getBookingDetailsById() }>
                    {'Search'}  
                  </Button>
                  </Col>
                  </>
                }

                {RoleId && ![12, 13].includes(RoleId) &&
                  <Col md={3} className="text-center mt-4">
                    <ManagerHierarchyV2 
                      handleShow={handleShow}
                      value={/UserID/g}
                      module={MY_BOOKINGS}
                      message={HIERARCHY_MESSAGE_NOTE}
                      />
                  </Col>
                }
              </Row>
              {/* <Messages RoleId={RoleId} /> */}

            </div>

            {IsLoading &&<><ShimmerLoader /><ShimmerLoader /></>}

            {
              !IsLoading &&
              <>
                {/* <div className="FilterSelection">
                  {BookingDescription && <h4 className="heading">{BookingDescription}</h4>}
                </div> */}
                <div className="spanbooking">
                  <table>
                    <thead>
                      <tr>
                        {
                          TableColumnsProductWise && TableColumnsProductWise.length > 0 &&
                          TableColumnsProductWise.map((item, index) => {
                            if (RoleId === 13 && item.name === 'SalesAgent') {
                            } else {
                              return <th key={index}>{item.name}</th>
                            }
                          })
                        }
                      </tr>
                    </thead>
                    <tbody>
                      {
                        !IsLoading && currentItems && currentItems.length > 0 &&
                        currentItems.map((data, serial) => {
                          let INDEX = SERIAL++;
                          return (
                            TableColumnsProductWise && TableColumnsProductWise.length > 0 &&
                            <>
                              <tr key={serial} id={`tr-${serial}`} className={OpenedLead === data.BookingID ? "" : "mobileViewToggle"} >
                                {
                                  TableColumnsProductWise.map((item, index) => {
                                    const ModifiedColumnData = ModifyType(data[item.accessor], item.type);
                                    return (
                                      <TableDataFormat
                                        key={serial + "-" + index}
                                        item={item}
                                        data={data}
                                        index={index}
                                        RoleId={RoleId}
                                        Index={INDEX}
                                        ModifiedColumnData={ModifiedColumnData}
                                        handleLinkClick={handleLinkClick}
                                        handlePopUp={handlePopUp}
                                        tableDataStates={tableDataStates}
                                        ProcessID= {ProcessID}
                                        handleOptWAClick={handleOptWAClick}
                                        handleAutoDebitPrompt={handleAutoDebitPrompt}
                                        handleOpenAddMomModal={() =>
                                          handleOpenAddMomModal(data.CustomerID, data.BookingID)
                                        } 
                                        handleOpenViewMomModal={() =>
                                          handleOpenViewMomModal(data.CustomerID, data.BookingID)
                                        } 
                                        />
                                    )
                                  })
                                }
                              </tr>
                              <div className="ViewMoreBtn">
                                <button id={`view-${serial}`} value={data.BookingID} onClick={(e) => HandleChangeOpenedLead(e)}> {OpenedLead === data.BookingID ? 'View Less' : 'View More'}   <i className="fa fa-angle-down" aria-hidden="true"></i>
                                </button>
                              </div>
                            </>
                          );
                        })
                      }
                    </tbody>
                  </table>
                </div>
                {
                  pageCount > 0 &&
                  <div id="bottomPagination" className="bottomPagination">
                    <Form.Select aria-label="Default select" width="20%" onChange={handleRowsCountChange} value={RowsPerPage}>
                      {NumberOfPages && NumberOfPages.map(item => {
                        return <option key={item.key} value={item.value}>{item.name}</option>
                      })}
                    </Form.Select>
                    <Pagination handlePageClick={handlePageClick} pageCount={pageCount} />
                  </div>
                }
                
              </>
            }
          </div>
        </Col>
      </Row>
      <button id={invisibleButtonState?.invisibleButtonId} style={{opacity:0}} ref={invisibleButton} onClick={checkInvisbleButton} >Invisible Button</button>
    </div>
  );
}

function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonspData,
    GetCommonspDataV2,
    GetCommonApiData,
    PostCommunicationData,
    GetCommonData
  }
)(MyBookings_V2);