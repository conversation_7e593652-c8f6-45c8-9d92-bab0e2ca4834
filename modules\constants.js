const tblList = {
    //Tables
    'INVProduct': 'MTX.INVProductGrpMapping_Allocation',
    'AlertMaster': 'dbo.alertmaster',
    'Users': 'CRM.UserDetails',
    'UsersWFH': 'CRM.UserDetails ud (NOLOCK) LEFT join Import.ZoneCities a (NOLOCK) on ud.cityid = a.CityID LEFT join Import.ZoneStates b (NOLOCK) on b.StateID = a.StateID',
    'RenewalUploads': 'CRM.RenewalUploads',
    'RenewalBulkUploadFileDetails': 'MTX.RenewalBulkUploadFileDetails',
    'CallingCom_CRMUserDetails': 'CRM.UserDetails',
    'UserGroup': 'CRM.UserGroupMaster',
    'vwUserGroup': 'CRM.UserGroupMaster (NOLOCK) UGM INNER JOIN CRM.ProductGroupMapping (NOLOCK) PGM ON UGM.UserGroupID = PGM.GroupId',
    'vwMatrixUserGroup': 'CRM.UserGroupMaster (NOLOCK) UGM INNER JOIN CRM.ProductGroupMapping (NOLOCK) PGM ON UGM.UserGroupID = PGM.GroupId where ISNULL(UGM.IsBMSGroup,0)=0',
    'Rooms': 'MTX.ChatData',
    'UserCallDetails': 'MTX.CallDataHistory (NOLOCK) CDH INNER JOIN CRM.UserDetails (NOLOCK) UD ON CDH.UserID = UD.UserID',
    'Suppliers': 'MTX.vwGetSupplierPlanName',
    'AgentGradeRules_Allocation': 'MTX.AgentGradeRules_Allocation',
    'AgentRules': 'MTX.AgentGradeRules_Allocation',
    'AgentRulesName': 'MTX.AgentGradeRules_Allocation',
    'AgentRulesWithId': 'MTX.AgentGradeRules_Allocation',
    'GradeRulesName': 'MTX.GradeRulesMapping_Allocation',
    'vwGradeRuleMapping': 'MTX.AgentGradeRules_Allocation (NOLOCK) AGR INNER JOIN mtx.GradeRulesMapping_Allocation (NOLOCK) GRM ON AGR.RuleID = GRM.RuleID',
    'GradeLimit': 'MTX.Grade_LimitMapping_Allocation',
    'HealthAgeGrpMapping': 'MTX.HealthAgeGrpMapping_Allocation',
    'NWHLogic': 'MTX.NWHLogic_Allocation',
    'TermProductGrpMapping': 'MTX.TermProductGrpMapping_Allocation',
    'ScoreMaster': '[MTX].[ScoreMaster]',
    'ProcessName_ScoreMaster': '[MTX].[ScoreMaster]',
    'PayTermBucketScore': '[MTX].[PayTermBucketScore]',
    'PremiumBucketScore': '[MTX].[PremiumBucketScore]',
    'ProcessName_PremiumBucketScore': '[MTX].[PremiumBucketScore]',
    'BrandScore': '[MTX].[BrandScore]',
    'AllocationProcessMaster': 'MTX.AllocationProcessMaster',
    'SupplierScore': '[MTX].[SupplierScore]',
    'AnnualIncomeScore': '[MTX].[AnnualIncomeScore]',
    'AgeBucketScoreMaster': '[MTX].[AgeBucketScoreMaster]',
    'CustomUtmScore': '[MTX].[CustomUtmScore]',
    'LeadSourceScore': '[MTX].[LeadSourceScore]',
    'LeadScoreRankMapping': '[MTX].[LeadScoreRankMapping]',
    'PayUScoreRankMapping': '[MTX].[PayUScoreRankMapping]',
    'LeadAgentRankMapping': 'MTX.LeadAgentRankMapping_NewApp',
    'ProductGrpMapping': 'MTX.ProductGrpMapping_Allocation',
    'Products': 'dbo.Products',
    'UserMenu': 'CRM.UserMenuMap',
    'SurveyResponse': '[MTX].[SurveyResponse]',
    'SurveyMaster': '[MTX].[SurveyMaster]',
    'SurveyNestedQuestionMaster': '[MTX].[SurveyNestedQuestionMaster]',
    'SurveyQuestionMaster': '[MTX].[SurveyQuestionMaster]',
    'BajajUpsellData': '[MTX].[BajajUpsellData]',
    'SurveyAgentMapping': '[MTX].[SurveyAgentMapping]',
    'SuperGroupMaster': 'enc.SuperGroupMaster',
    'SuperGroupTypeMaster': '[enc].[SuperGroupTypeMaster]',
    'EMIFailedStatusMaster': '[MTX].[EMIFailedStatusMaster]',
    'ProcessIncentiveFile': 'ENC.UploadIncentiveFile',
    'AdvisorInfo': 'MTX.AdvisorInfo',
    'AdvisorInfoStarRating': 'MTX.AdvisorInfoStarRating',
    'smsTriggerInfo': 'MTX.smsTriggerInfo',
    'QuizMaster': 'enc.Lottery_QuizMaster',
    'CountryMaster': '[master].[Country]',
    'ContestAgentMapping':'[ENC].[ContestAgentMapping]',
    'FOS_AppointmentSlots' : '[master].[FOS_AppointmentSlots]',
    'UserProducts' : 'MTX.userBuMapping (NOLOCK) UBM LEFT JOIN dbo.Products (NOLOCK) PDT ON UBM.ProductId = PDT.ID',
    'healthhnilogic' : 'master.healthhnilogic',
    'CMAgeBucketScoreMaster': '[MTX].[CMAgeBucketScoreMaster]',
    'AdultChildScoreMaster': '[MTX].[AdultChildScore]',
    'CityScoreMaster': '[MTX].[CityScore]',
    'GroupProcessMaster': 'MTX.GroupProcessMaster',

    //Stored Procedures
    'GetUserInfoMtxDashboard': '[MTX].[GetUserInfoMtxDashboard]',
    'GetSupervisor': '[MTX].[GetSupervisor]',
    'GetProducts': '[MTX].[GetProducts]',
    'AgentLoginTracker': '[MTX].[AgentLoginTracker]',
    'GetAgentIdleTime': '[MTX].[GetAgentIdleTime]',
    'CallBackTracker': '[MTX].[CallBackTracker_New]',
    //'CallBackTracker': '[MTX].[CallBackTracker_New_V2]',
    'AssignedToAgent': '[CRM].[Insert_AssignedToAgent]',
    'GetTLList': '[MTX].[GetTLList]',
    'GetAgentStats': '[MTX].[GetAgentStats]',
    'GetUserStats': '[MTX].[GetUserStats]',
    'AgentLeadsNotCalled': '[MTX].[AgentLeadsNotCalled]',
    'CheckSurveyAgent': '[MTX].[CheckSurveyAgent]',
    'UploadVideoData': '[MTX].[GetPreLoginPageSurveyList]',
    'InsertVideoData': '[MTX].[InsertPreLoginPageSurveyByPanel]',
    'InsertPaymentFailedLeads': '[MTX].[InsertPaymentFailedLeads]',   
    'InsertAgentIncentiveLog': '[enc].[InsertINCAgentIncentiveOpenLog]',   
    'AgentsIncentiveCriteria': '[enc].[GetSuperGroupIncentiveMapping]',
    'InsertAgentsIncentiveCriteria': '[enc].[InsertIncentiveGroupCriteria]',
    'SuperGroupCriteriaHtml': '[enc].[SuperGroupCriteriaHtml]',
    'C2CTrackingDataService': '[MTX].[C2CTrackingDataService]',
    'RenewalPaymentFailed': '[CRM].[SavePaymentFaileddetails]',   
    'CallBackAgentWise': 'MTX.CallBackAgentWise',
    'GetUserPODLeads': '[MTX].[GetUserPODLeads]',
    'GetPODLeadsData': '[MTX].[GetPODLeadsData]',
    'GetAgentPODBookings': '[MTX].[GetAgentPODBookings]',
    'GetAllRejectedLeads': '[MTX].[GetAllRejectedLeads]',
    'GetQuoteLeads': '[CRM].[GetQuoteleadsByagentId]',
    'EmiPaymentFailed': '[MTX].[GetPaymentFailedLeads]',
    'UpdateEMIPaymentStatus': '[MTX].[UpdateEMIPaymentStatus]',
    'GetUniqueGroups': '[CRM].[GetUniqueGroups]',

    'GetIncentiveFiles': '[ENC].[GetIncentiveFiles]',
    'GetKYCTriggerInfo': '[MTX].[GetKYCTriggerInfo]',
    'InsertAdvisorInfoStarRating':'MTX.InsertAdvisorInfoStarRating',
    'GetFosAppointmentData': '[FOS].[GetFosAppointmentData]',
    'GetFosAllocationPanel': '[MTX].[GetFosAllocationPanel]',
    'GetFosAllocationPanelNew': '[MTX].[GetFosAllocationPanelv2]',
    'GetFosAllocationAgent':'[MTX].[GetFosAllocationAgent]',
    'SetFosUnassignLeads' : '[MTX].[SetFosUnassignLeads]',
    'GetFosGroups':'[MTX].[GetFosGroups]',
    'GetSubStatus':'[MTX].[GetSubStatusForDashBoard]',
    'GetUserProductList': '[MTX].[GetUserProductList]',
    'InsertSurveyAgentMapping':'[MTX].[InsertSurveyAgentMapping]',
    'Dkd_AgentDetail' : '[enc].[Dkd_AgentDetail]',
    'Dkd_LotteryTickets': '[enc].[Dkd_LotteryTickets]',
    'Fos_FosAssignmentProducts':'[MTX].[Fos_FosAssignmentProducts]',
    'GetAgentDashboardDetails':'[FOS].[GetAgentDashboardDetails_v2]', 
    //'GetAgentDashboardDetails':'[FOS].[GetAgentDashboardDetailsTest]', 
    'GetSmeQuotesByAgentId':'[MTX].[GetSmeQuotesByAgentId]',
    'InsertSurveyAgentMappingAll' : '[MTX].[InsertSurveyAgentMappingAll]',
    'GetAgentCSATScore':'MTX.GetAgentCSATScore',
    'GetRealTimeDashboardDetails': '[FOS].[GetRealTimeDashboardDetails]',
    'GetAgentProcessOnProduct':'MTX.GetAgentProcessOnProduct',
    'InsertSurveyAgentsProcess' : 'MTX.InsertSurveyAgentsProcess',
    'GetAllLeads':'[MTX].[GetlLeadsDetailsDashboard]',
    'GetSubProductType' : '[CRM].[GetSubProductType]',
    'GetCustQuesAns':'[MTX].[GetCustomerQuestionAnswer]',
    'CreateLead': '[MTX].[CreateLeadDashboard]',
    'GetStateCitiesList': '[MTX].[GetStateCityList]',
    'GetCarMakeList': '[MTX].[GetCarMakeList]',
    'GetFOSSelfieData' : '[FOS].[GetSelfieData]',
    'GetSelfieFailReasons' : '[FOS].[GetSelfieFailReasons]',
    'GetSelfieQuestionsOptions' : '[FOS].[GetSelfieQuestionsOptions]',
    'InsertSelfieData' : '[FOS].[InsertSelfieData_v1]',
    'GetDashboardUserDetails' : '[MTX].[GetDashboardUserDetails]',
    'GetRenewalUploadsData' : '[CRM].[GetRenewalUploadsData]',
    'GetSmeRenewalUploadsData' : '[MTX].[GetSmeRenewalUploadsData]',
    'GetRenewalBulkUploadFileDetailsData' : '[CRM].[GetRenewalBulkUploadFileDetailsData]',
    'GetSmeRenewalBulkUploadFileDetailsData' : '[MTX].[GetSmeRenewalBulkUploadFileDetailsData]',
    'GetAppointmentsTracking' : '[FOS].[GetAppointmentsTracking_v2]',
    'GetAppointmentSlots' : '[FOS].[GetAppointmentSlots]',
    'GetAgentListByManagerId' : '[MTX].[GetAgentListByManagerId_v1]',
    'GetAppointmentTypebyProduct':'[MTX].[GetAppointmentTypeList]',
    'GetQueueName':'[MTX].[GetQueueName]',
    'GetOfflineCities':'[FOS].[GetAllOfflineCities]',
    'UpdateAssignmentType':'[FOS].[UpdateAssignmentType]',
    // 'GetAppointmentsHistoryReport':'[FOS].[GetAppointmentsHistoryReport]',
    'GetAppointmentsTrackingNextDay':'[FOS].[GetAppointmentsTrackingNextDay]',
    'GetBookingDetailsPrimary' : '[MTX].[GetBookingDetailsPrimaryV2]',
    'GetBookingDetailsSecondary' : '[MTX].[GetBookingDetailsSecondaryV2]',    
    'GetFosTLDashboardData':'[FOS].[GetFosTLDashboardData]',
    'GetLeadRemarks': '[MTX].[GetLeadRemarks]',
    'SaveLeadHistory':'[MTX].[SaveLeadHistory]',
    'UpdatePGAutoPayStatus':'[MTX].[UpdatePGAutoPayStatus]',
    
    'GetGroupByProductId' : '[CRM].[GetGroupByProductId]',
    'Agent_LeadRankMapping':'[MTX].[Agent_LeadRankMapping]', 
    'GetUserProfile': 'MDB.GetUserProfile',
    'ContestMaster': '[ENC].[ContestMaster]',
    'ContestApplicationMaster' : '[ENC].[ContestApplicationMaster]',
    'ContestOptionMaster': '[ENC].[ContestOptionMaster]',
    'InsertContestAgentMapping':'[ENC].[InsertContestAgentMapping]',
    'GetTenureWiseAchievement':'[MTX].[GetTenureWiseAchievement]',
    'GetAMSummary':'[MTX].[GetAMSummary]',

    'GetNearestAgentToLead':'[FOS].[GetNearestAgentToLead]',
    //'GetRewards':'[ENC].[GetRewards]',
    'CopyContestOptions':'[ENC].[CopyContestOptions]',
    'GetUserlistbyManagerId':'mtx.GetUserlistbyManagerId',
    'GetMultiLoginByProduct' : '[MTX].[GetMultiLoginByProduct]',

    // SPs from Other SQL DB
    'GetSosAgentDocList': '[dbo].[Term_AgentDocStatus_CRT]', // AnanlyticsLife
    'GetCallRecordings':'[MTX].[GetCallRecordings]',
    'InsertUpdateCallRecordingLog':'[MTX].[InsertUpdateCallRecordingLog]',
    'GetDetailsForKYA':'[MTX].[GetDetailsForKYA]',
    'InsertKYAVisitLog' : '[MTX].[InsertKYAVisitLog]',
    'GetAgentFOSLeadAssignDetails' : '[MTX].[GetAgentFOSLeadAssignDetails]',
    'GetStoryList' : '[MTX].[GetAgentStoryList]',
    'InsertRenewalBulkUploadFile':'[MTX].[InsertRenewalBulkUploadFile]',

    // Mongo Collections
    'History': 'UpdateHistory',
    'livechat_department': 'rocketchat_livechat_department',
    'livechat_department_agents': 'rocketchat_livechat_department_agents',
    'JagStoryBoard': 'JagStoryBoard',
    'users': 'users',
    'roles':'rocketchat_roles',
    'DashboardLogs': 'MatrixDashboardLogs',
    'Settings' : 'Settings',
    'ContestAgents' : 'LotteryContestAgents',
    'SMEConfig':'SMEConfig',
    // 'BargeLogs': 'BargingLogs',
    'FileProcessingErrorLogs': 'FileProcessingErrorLogs',
    'TermAgentLevelData': 'TermAgentLevelData',
    'TermBookingLevelData': 'TermBookingLevelData',
    'HealthAgentLevelData': 'HealthAgentLevelData',
    'HealthBookingLevelData': 'HealthBookingLevelData',
    'HealthRenewalAgentLevelData': 'HealthRenewalAgentLevelData',
    'MotorAgentLevelData': 'MotorAgentLevelData',
    'MotorBookingLevelData': 'MotorBookingLevelData',
    'InvestmentAgentLevelData': 'InvestmentAgentLevelData',
    'InvestmentBookingData': 'InvestmentBookingData',
    'MotorPolicyTypeData': 'MotorPolicyTypeData',
    'MyBookingsProducts': 'MyBookingsProducts',
    'FosUnassignedLeads': 'FosUnassignedLeads',
    'RMInsurerList' : 'RMInsurerList',
    
    //MySQL Tables
    'InboundQueueMaster': 'inbound_queue_master',
    'CampaignRoute': 'campaign_route',
    'QueueRoute': 'queue_route',
    'LanguageMaster': 'master.RegionalLanguage',
    'LocationMaster': 'master.Location',
    'AgentProcessMaster': 'master.AgentProcess'
};

const tblListWithParams = {

    'GetMyBookingDetails' : {
        proc: '[MTX].[GetBookingDetailsPrimaryV2]',
        params: [
            {element: 'AgentID', from: 'user', userKey: 'userId'}, {element: 'BookingType', from: ''},
            {element: 'BookingMonth', from: ''}, {element: 'ProductIds', from: ''}, 
           {element: 'ManagerIds', from: ''}, {element: 'FilterProduct', from: ''}, {element: 'ProductPresent', from: ''} 
        ]
    },
    // 'GetMyBookingDetailsSecondary' : {
    //     proc: '[MTX].[GetBookingDetailsSecondaryV2]',
    //     params: [
    //         {element: 'AgentID', from: 'user', userKey: 'userId'}, {element: 'ProductID', from: ''}, {element: 'BookingType', from: ''}, {element: 'daterangeFlag', from: ''},
    //          {element: 'BookingMonth', from: ''},
    //           {element: 'BookingStatusType', from: ''}, {element: 'ManagerIds', from: ''},
    //     ]
    // },
    'GetLeadRemarks':  {
        proc: '[MTX].[GetLeadRemarks]',
        params: [
             {element: 'BookingID', from: ''},
        ]
    },
    'GetReferralsAndBookingStatus':  {
        proc: '[MTX].[GetReferralsAndBookingStatus]',
        params: [
             {element: 'LeadID', from: ''},
        ]
    },
    'SaveLeadHistory':  {
        proc: '[MTX].[SaveLeadHistory]',
        params: [
             {element: 'LeadID', from: ''},
             {element: 'UserID', from: 'user', userKey: 'userId'},
             {element: 'StatusId', from: ''},
             {element: 'SubstatusId', from: ''},
             {element: 'EventType', from: ''},
             {element: 'Comments', from: ''},
             {element: 'Type', from: ''},
              
        ]
    },
    'GetUserProfile':  {
        proc: '[MDB].[GetUserProfile]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'}    
        ]
    },
    'GetDuplicateLeads' : {
        proc: '[MTX].[GetDuplicateLeads]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
            {element: 'UserInput', from: ''},
            {element: 'SearchType', from: ''}
        ]
    },
    'GetSuppliersAndPlans':  {
        proc: '[CRM].[Get_SuppliersAndPlans]',
        params: [
             {element: 'ProductName', from: ''},
             {element: 'ProductId', from: ''}
        ]
    },
    'GetSupervisor':  {
        proc: '[MTX].[GetSupervisor]',
        params: [
             {element: 'RoleId', from: ''},
             {element: 'ProductId', from: ''}
        ]
    },
    'AgentCrossSellLeads' : {
        proc: '[MTX].[AgentCrossSellLeads]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
            {element: 'FromDate', from: ''},
            {element: 'ToDate', from: ''}
         ]
    },
    'GetFollowUpPermissionsByUserId' : {
        proc: '[CRM].[GetFollowUpPermissionsByUserId]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
         ]
    }, 

    'GetRewards':  {
        proc: '[ENC].[GetRewards]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
             {element: 'PrizeType', from: ''},
        ]
    },
    'UdateUserAuditHistory':  {
        proc: '[CRM].[UdateUserAuditHistory]',
        params: [
            {element: 'userId', from: ''},
            {element: 'comments', from: ''},
            {element: 'actionBy', from: 'user', userKey: 'userId'},
        ]
    },
    'GetAgentFromSupervisor':  {
        proc: '[MTX].[GetAgentFromSupervisor]',
        params: [
             {element: 'ProductID', from: ''},
             {element: 'SupervisorId', from: ''}
        ]
    },
    'GetUserlistbyManagerId': {
        proc: '[mtx].[GetUserlistbyManagerId]',
        params: [
            {element: 'productId', from: ''},
            {element: 'managerId', from: 'user', userKey: 'userId'}
        ]
    },
    'GetUserProductList': {
        proc: '[mtx].[GetUserProductList]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'}
        ]
    },
    'GetPaymentOverDueCountByManagerId': {
        proc: '[mtx].[GetPaymentOverDueCountByManagerId]',
        params: [
            {element: 'ProductID', from: ''},
            {element: 'ManagerId', from: 'user', userKey: 'userId'}
        ]
    },
    'GetAppointmentJourney':{
        proc:'[FOS].[GetAppointmentJourney]',
        params: [
            {element:'LeadId', from:''}
        ]
    },
    'GetAppointmentsByUserId':{

    proc:'[FOS].[GetAppointmentsByUserId]',
    params:[
        {element:'UserId', from: ''},
        {element:'LeadId', from: ''}
    ]

    },
    'GetSmeRenewalBulkUploadFileDetailsData':{
        proc:'[MTX].[GetSmeRenewalBulkUploadFileDetailsData]',
        params:[
            {element:'AgentId', from: 'user', userKey: 'userId'},
            {element:'ProcessId', from: ''}
        ]
    },
    'GetSmeRenewalUploadsData':{
        proc:'[MTX].[GetSmeRenewalUploadsData]',
        params:[
            {element:'UploadedBy', from: 'user', userKey: 'userId'},
            {element:'Source', from: ''},
            {element:'TrackingId', from:''}
        ]
    },
    'GetRenewalUploadsData': {
        proc: '[CRM].[GetRenewalUploadsData]',
        params: [
            { element: 'UploadedBy', from: 'user', userKey: 'userId' },
            { element: 'Source', from: '' },
            { element: 'TrackingId', from: '' },
        ]
    },
    'GetRenewalBulkUploadFileDetailsData': {
        proc: '[CRM].[GetRenewalBulkUploadFileDetailsData]',
        params: [
            { element: 'UploadedBy', from: 'user', userKey: 'userId' }
            , { element: 'ProductId', from: '' }
        ]
    },
    'GetDuplicateLeadsByProductId' : {
        proc: '[MTX].[GetDuplicateLeadsByProductId]',
        params: [
            {element: 'UserId', from: 'user', userKey: 'userId'},
            {element: 'UserInput', from: ''},
            {element: 'SearchType', from: ''},
            {element: 'ProductId', from: ''}
        ]
    },
    'GetGroupsByUserId': {
        proc: '[CRM].[GetGroupsByUserId]',
        params: [
            { element: 'UserId', from: 'user', userKey: 'userId' }
        ]
    },
    'Renewal_AssignedToAgent' : {
        proc: '[MTX].[Renewal_AssignedToMultipleAgent]',
        params: [
            { element: 'UserId', from: 'user', userKey: 'userId' },
            { element: 'AssignedToList', from: '' },
            { element: 'StartDate', from: '' },
            { element: 'EndDate', from: '' },
            { element: 'AgentId', from: '' },
            { element: 'SupplierID', from: '' },
            { element: 'NoticePremium', from: '' },
            { element: 'RenewalType', from: '' },
            { element: 'ProductId', from: '' },
            { element: 'UniqueId', from: '' }
        ]
    },
    'AssignedToRenewalAgent':{
        proc: '[MTX].[Fetch_Update_AssignedToMultipleAgent]',
        params: [
            { element: 'UniqueId', from: '' }
        ]
    },
    'GetAllUserGroups':{
        proc: '[CRM].[GetAllUserGroup]',
        params: [
            { element: 'ProductId', from: '' }
        ]
    },
    'InsertUserGroup':{
        proc: '[CRM].[InsertUserGroup]',
        params: [
            { element: 'UserGroupName', from: '' },
            { element: 'ProductId', from: '' },
            { element: 'StrSubProductTypeId', from: '' },
            { element: 'IsAsterick', from: '' },
            { element: 'CallingType', from: '' },
            { element: 'IsOneLead', from: '' },
            { element: 'ProcessID', from: ''},
            { element: 'LanguageID', from: ''},
            { element: 'Location', from: ''},
            { element: 'AgentProcess', from: ''}
        ]
    }
}

module.exports = {
    ...tblList,
    ...tblListWithParams
};