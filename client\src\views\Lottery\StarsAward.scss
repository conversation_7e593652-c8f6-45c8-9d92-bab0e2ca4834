@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,700;1,800;1,900&display=swap');

.wrapper {
    background: transparent radial-gradient(closest-side at 52% 52%, #aa60d8 0%, #522A7E 71%, #020D37 100%) 0% 0% no-repeat padding-box !important;
}

.main-panel {
    background: transparent !important;
    display: flex;
    align-items: center;
    justify-content: center;

    .confetti-bg {
        position: fixed;
        top: -153px;
        left: 0;
        width: 100vw;
        height: 100vh;
        pointer-events: none;
        z-index: 1;
        background: url('../../../public/lottery/Award/confetti.png') 0% 0% no-repeat padding-box;
        background-size: 100%;
        background-position: center;

    }

    .EmergingStarsLayout {
        width: 100%;
        display: flex;
        align-items: center;
        height: 100vh;

        .BoxLayout {
            background: #ffffff1f 0% 0% no-repeat padding-box;
            border-radius: 12px;
            width: 1100px;
            padding: 20px 0px;
            height: 550px;
            margin: auto;
            position: relative;

            .Header {
                display: flex;
                justify-content: space-between;
                width: 100%;
                align-items: flex-start;
                position: relative;
                padding: 0px 35px;

                .star {
                    text-align: center;
                    position: absolute;
                    left: 0;
                    margin: auto;
                    right: 0;
                    width: 540px;
                    top: -70px;

                    img {
                        width: 180px;
                    }

                    h3 {
                        text-align: center;
                        font: normal normal bold 38px/48px Poppins;
                        letter-spacing: 0px;
                        color: #FFFFFF;
                        text-transform: uppercase;
                        opacity: 1;
                    }
                }
            }

            .RightLayout {
                width: 52%;
                float: left;
                position: relative;
                left: -30px;

                img {
                    margin: 35px 0px 10px;
                }

                fieldset {
                    background: #FFFFFF0D 0% 0% no-repeat padding-box;
                    border: 1px dashed #ddd;
                    border-radius: 8px;
                    opacity: 1;
                    width: 480px;
                    margin: 20px 0px;
                    display: inline-block;

                    legend {
                        padding: 0px 5px;
                        font: normal normal 600 20px/25px Poppins;
                        letter-spacing: 0px;
                        margin-bottom: 0px;
                        width: auto;
                        float: none;
                        padding-bottom: 0px;
                        color: #FFFFFF;
                        opacity: 1;
                        text-align: center;
                    }

                    input {
                        width: 100%;
                        height: 45px;
                        background: transparent;
                        border: none;
                        text-align: center;
                        font: normal normal 500 35px/63px Roboto;
                        letter-spacing: 19px;
                        color: #FFFFFF;

                    }

                }

                .EmployeeName {
                    width: 100% !important;
                    text-align: center;
                    font-size: 22px;
                    font-weight: 600;
                    margin-bottom: 30px;
                    color: #FFF115;
                    font: normal normal 600 35px/50px Poppins;

                    p {
                        font: normal normal 600 23px/35px Poppins;
                    }

                }

                ul {
                    padding-left: 0px;
                    list-style-type: none;
                    color: #fff;
                    display: flex;
                    flex-wrap: wrap;
                    margin-left: 100px;

                    li {
                        width: 50% !important;
                        margin-top: 5px;
                        padding: 0px 10px;
                        font: normal normal normal 17px/40px Poppins;


                        &:nth-child(2n+2) {
                            padding-left: 40px;

                        }

                        strong {
                            display: block;
                            font: normal normal 600 20px/30px Poppins;
                            white-space: nowrap;
                            width: 100%;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }


                    }





                }


                button {
                    background: #321E66 0% 0% no-repeat padding-box;
                    border-radius: 8px;
                    position: relative;
                    text-align: center;
                    font: normal normal bold 21px/29px Roboto;
                    letter-spacing: 0px;
                    color: #FFFFFF;
                    text-transform: capitalize;
                    opacity: 1;
                    border: none;
                    width: 50px;
                    height: 50px;
                    top: -10px;
                    left: 10px;
                }
            }

            .LeftLayout {
                width: 48%;
                float: left;
                position: relative;
                height: 448px;
                overflow: hidden;
                border-radius: 10px;

                .circle {
                    background-color: #ffffff3b;
                    height: 573px;
                    width: 100%;
                    float: right;
                    position: relative;
                    top: 50px;
                    left: -84px;
                    z-index: 999999999999;
                    border-radius: 50%;
                    bottom: 0;

                    img {
                        margin: auto;
                        position: absolute;
                        top: 60px;
                        width: 377px;
                        left: 56px;
                        right: 0;
                    }

                    .carFlip {
                        -webkit-transform: scaleX(-1);
                        transform: scaleX(-1)
                    }

                    .lottieAnimation {
                        position: relative;
                        bottom: 85px;
                    }
                }
            }

            .LotteryTicketPage {
                text-align: center;
                display: flex;
                flex-direction: column;
                align-items: center;

                fieldset {
                    background: #FFFFFF0D 0% 0% no-repeat padding-box;
                    border: 1px dashed #ddd;
                    border-radius: 8px;
                    opacity: 1;
                    width: 300px;
                    margin-top: 8px;
                    display: inline-block;
                    height: 85px;

                    legend {
                        padding: 0px 5px;
                        font: normal normal 600 20px/25px Poppins;
                        letter-spacing: 0px;
                        margin-bottom: 0px;
                        width: auto;
                        float: none;
                        padding-bottom: 0px;
                        color: #FFFFFF;
                        opacity: 1;
                        text-align: center;
                    }

                    input {
                        width: 100%;
                        background: transparent;
                        border: none;
                        text-align: center;
                        font: normal normal 500 45px/59px Roboto;
                        letter-spacing: 19px;
                        color: #FFFFFF;

                    }

                }

                button {
                    background: #321E66 0% 0% no-repeat padding-box;
                    border-radius: 8px;
                    text-align: center;
                    font: normal normal bold 21px/29px Roboto;
                    letter-spacing: 0px;
                    color: #FFFFFF;
                    text-transform: capitalize;
                    opacity: 1;
                    border: none;
                    width: 96px;
                    height: 50px;
                    margin-top: 15px;
                }

                h1 {
                    font: normal normal bold 55px/55px Poppins;
                    letter-spacing: 0px;
                    text-shadow: 4px 2px #724b99d4;
                    color: #FFFFFF;
                    text-transform: uppercase;
                    opacity: 1;
                    margin-bottom: 15px;
                }

                h4 {
                    font: normal normal bold 29px/43px Poppins;
                    letter-spacing: 0px;
                    color: #FFFFFF;
                    text-transform: uppercase;
                    opacity: 1;
                }
            }

            .WelcomeLayout {
                width: 800px;
                margin: auto;
                text-align: center;
                position: relative;
                top: -25px;
                height: 420px;

                h2 {

                    text-align: center;
                    font: normal normal bold 48px/50px Cinzel;
                    letter-spacing: 0px;
                    color: #FFF115;
                    text-transform: uppercase;
                    opacity: 1;
                    text-shadow: 1px 1px 0 #a49b12, -1px -1px 0 #FFFBC1;
                }

                h1 {
                    text-align: center;
                    font: normal normal bold 66px/70px Poppins;
                    letter-spacing: 0px;
                    color: #FFFFFF;
                    margin-bottom: 0px;
                    text-transform: uppercase;
                    opacity: 1;
                    text-shadow: 4px 2px #724b99d4;

                    img {
                        width: 49px;
                    }
                }

                .LottryTicketImage {
                    margin: auto;
                    position: relative;
                    width: 300px;
                    top: -59px;
                    height: 275px;

                    .lightEffect {
                        position: relative;
                        width: 380px;
                        -webkit-animation: spin 4s linear infinite;
                        -moz-animation: spin 4s linear infinite;
                        animation: spin 4s linear infinite;
                    }

                    @-moz-keyframes spin {
                        100% {
                            -moz-transform: rotate(360deg);
                        }
                    }

                    @-webkit-keyframes spin {
                        100% {
                            -webkit-transform: rotate(360deg);
                        }
                    }

                    @keyframes spin {
                        100% {
                            -webkit-transform: rotate(360deg);
                            transform: rotate(360deg);
                        }
                    }

                    .ticketImg {
                        position: absolute;
                        left: 0;
                        top: 25px;
                        right: 0;
                        margin: auto;
                        width: 250px;
                        bottom: 0;
                    }

                    .BtnDesign {
                        position: absolute;
                        left: 0;
                        right: 0;
                        margin: auto;
                        bottom: 0;

                        button {
                            background: #321e663b 0% 0% no-repeat padding-box;
                            box-shadow: 0px 3px 6px #AB92EB10;
                            border-radius: 8px;
                            text-decoration: underline;
                            font: normal normal 500 17px/26px Roboto;
                            letter-spacing: 0px;
                            color: #FFFFFF;
                            padding: 6px 18px;
                            margin: 0px 6px;
                            text-transform: uppercase;
                            border: none;
                        }
                    }
                }
            }

            .Car-footerBg {
                position: absolute;
                left: -15px;
                bottom: -7px;
                width: 270px;
                cursor: pointer;
            }

            .House-footerBg {
                position: absolute;
                right: -40px;
                bottom: -10px;
                width: 270px;
                cursor: pointer;
            }

            .backButton {
                background: #875bb454 0% 0% no-repeat padding-box;
                border-radius: 8px;
                color: #fff;
                font: normal normal 600 15px/30px Poppins;
                width: 118px;
                position: absolute;
                box-shadow: none;
                height: 35px;
                top: -40px;
                border: none;
            }
        }

        .giftbgImage {
            bottom: 0px;
            position: fixed;
            right: 0;
            width: 450px;
        }

        .JagCountTimer {
            position: absolute;
            width: 100%;
            left: 0;
            z-index: 99999;
            right: 0;
            top: 0;
            bottom: 0;
            background-color: #000000d1;

            .lottieAnimation {
                width: 500px;
                position: absolute;
                left: 0;
                right: 0;
                bottom: 0;
                top: 0;
                margin: auto;
            }
        }
    }
}