
import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Form } from 'react-bootstrap';

import {
   InsertData, UpdateData, GetCommonspData, PostUploadVideoFormData
} from "../store/actions/CommonAction";
import {
  addRecord, GetCommonData
} from "../store/actions/CommonMongoAction";

import { If, Then } from 'react-if';
//import { If, Then, Else, When, Unless, Switch, Case, Default } from 'react-if';

import { connect } from "react-redux";
// import { Link } from "react-router-dom";
import DataTable from './Common/DataTableWithFilter';
import DropDown from './Common/DropDownList';
import AlertBox from './Common/AlertBox';
import moment from 'moment';
import {  fnBindRootData, fnRenderfrmControl, fnDatatableCol, fnCleanData, joinObject } from '../utility/utility.jsx';
// reactstrap components
import {
  Card,
  CardHeader,
  CardBody,
  CardT<PERSON>le,
  <PERSON>,
  Row,
  Col
} from "reactstrap";
import { getuser } from "utility/utility";
import 'react-toastify/dist/ReactToastify.css';
import _, { bind } from 'underscore';

class UploadVideo extends React.Component {
  constructor(props) {
    super(props);
    this.columnlist = [
      {
        name: "SurveyLocation",
        label: "Banner Placement",
        type: "dropdown",
        config: {
          root: "SurveyLocation",
          firstoption: 'PreLogin',
          firstoptionvalue: 'PreLogin'
        },
        editable: false
      },
      {
        name: "SurveyName",
        label: "Enter Video Title",
        type: "string",
        searchable: true,
        cell: row => <Cell v={row.SurveyName} />,
      },
      {
        name: "pageId",
        label: "pageId",
        type: "int",
        hide: true,
        addable: false,
        editable: false,
        hideonmodal: true,
      },
      {
        name: "SurveyId",
        label: "Survey Id",
        type: "int",
        hide: true,
        addable: false,
        editable: false,
        hideonmodal: true,
      },
      {
        name: "EmployeeId",
        label: "EmployeeId",
        type: "string",
        searchable: true,
        editable: false,
        addable: false,
        cell: row => <Cell v={row.EmployeeId} />,
        width: "200px",
        hideonmodal: true,
      },
      {
        name: "Product",
        label: "Product",
        type: "dropdown",
        // config: {
        //   root: "Products",
        //   cols: ["ID AS Id", "ProductName AS Display"],
        //   con: [{ "Isactive": 1 }]
        // },
        config: {
          root: "ProductVideoUpload",
          data: [{Id : 139, Display: "Commercial"}, { Id: 2, Display: "Health" },{Id : 101, Display: "Home"},{ Id: 115, Display: "Investment" },{Id: 117, Display: "Motor"},{Id: 7 , Display: "TermLife"},{Id: 3 , Display: "Travel"},{Id: 114, Display: "Twowheeler"},{Id: 131, Display: "SME/GMC"}],
        },
        searchable: false,
        editable: false,
        addable: false,
        hideonmodal: true
      },
      {
        name: "ProductID",
        label: "Product",
        type: "dropdown",
        // config: {
        //   root: "Products",
        //   cols: ["ID AS Id", "ProductName AS Display"],
        //   con: [{ "Isactive": 1 }]
        // },
        config: {
          root: "ProductVideoUpload",
          data: [{Id : 139, Display: "Commercial"}, { Id: 2, Display: "Health" },{Id : 101, Display: "Home"},{ Id: 115, Display: "Investment" },{Id: 117, Display: "Motor"},{Id: 7 , Display: "TermLife"},{Id: 3 , Display: "Travel"},{Id: 114, Display: "Twowheeler"},{Id: 131, Display: "SME/GMC"}],
        },
        hide: true,
        editable: false,
        disabled: false,
        searchable: false,
      },
      {
        name: "RoleId",
        label: "Role",
        type: "multiselect",
        searchable: true,
        distinct: true,
        config: {
          root: 'roleids',
          data: [{ Id: 13, Display: "Agent" }, { Id: 12, Display: "Supervisor" }],
        },
        hide: true,
        editable: false,
        disabled: false

      },// None value should be always 1 

      {
        name: "RoleName",
        label: "RoleName",
        type: "string",
        searchable: true,
        distinct: true,
        editable: false,
        addable: false,
        cell: row => <Cell v={row.RoleName} />,
        width: "200px",
        hideonmodal: true,
      },
      {
        name: "GroupId",
        label: "Group",
        type: "multiselect",
        searchable: true,
        distinct: true,
        config: {
          root: "vwMatrixUserGroup",
          cols: ["DISTINCT UGM.UserGroupID AS Id", "UGM.UserGroupName AS Display", "ProductID"],
        },
        hide: true,
        editable: false,
        disabled: false
      },
      {
        name: "UserGroupName",
        label: "GroupName",
        type: "string",
        searchable: true,
        distinct: true,
        editable: false,
        addable: false,
        cell: row => <Cell v={row.UserGroupName} />,
        width: "200px",
        hideonmodal: true,

      },
      {
        name: "StartDate",
        label: "FromDate",
        type: "datetime",
        //utc: "no",
      },
      {
        name: "EndDate",
        label: "ToDate",
        type: "datetime",
        //utc: "no",
      },
      {
        name: "IsRequired",
        label: "IsRequired",
        type: "bool"
      },
      {
        name: "IsActive",
        label: "IsActive",
        type: "bool"
      },
      {
        name: "MultiLoginCount",
        label: "MultiLoginCount",
        type: "dropdown",
        config: {
          root: "MultiLoginCount",
          data: [ { Id: 2, Display: 2 }, { Id: 3, Display: 3 }],
        },
        editable: false,
        hide: true
      },
      {
        name: "MultiLoginCount",
        label: "MultiLogin",
        type: "int",
        addable: false,
        editable: false,
        hideonmodal: true,
      },
      {
        name: "ContentType",
        label: "ContentType",
        type: "dropdown",
        config: {
          root: "ContentType",
          data: [{ Id: 2, Display: "Video" }, { Id: 4, Display: "Image" }],
        },
        editable: false,
      },
      {
        name: "Video",
        label: "Choose File",
        type: "video",
        editable: false,
        hide: true,
      },
      {
        name: "Link",
        label: "Link",
        type: "string",
        editable: true,
        cell: row => <a href={row.Link} target="_blank"><Cell v={row.Link} /></a>,
        width: "200px",
        disabled: false
      },
      {
        name: "Listen",
        label: "View",
        cell: row =>
          <div className="listenUserDetails">
            <a onClick={(e) => this.getHtmlListen(e, row)} className="detailsinfo">
              <i className="fa fa-eye"></i></a>
          </div>,
        editable: false,
        addable: false,
        hideonmodal: true,
      },

    ];
    this.state = {
      root: "UploadVideoData",
      IsLoading: false,
      showModal: false,
      items: [],
      store: [],
      activePage: 1,
      //root: "ProductGrpMapping",
      PageTitle: "Upload Video",
      FormTitle: "",
      formvalue: {},
      event: "",
      ModalValueChanged: false,
      user: {},
      prolist: '',
      agentranklist: '',
      groupnamelist: '',
      selectedFile: null,
      VideoData: [],
      PostUploadVideoFormData: [],
      errors: {},
      addClass: 'btn btn-primary',
      clickVideo: false,
      clickModalUrl: '',
      content: '',
      PageId: null,
      formColumnList: this.columnlist 
      //selectedrow: { "Id": 1, "type": "Job", "alerttitle": "Mongoquery2", "querytype": "MongoQuery", "databasename": "mydb", "collection": "movie", "dbserver": "Replica", "tomail": "<EMAIL>,<EMAIL>", "tomobile": "99688436", "count": 4, "operation": "lt", "starttime": "1970-01-01T10:28:04.000Z", "frequency": 120, "IsActive": false, "reporttype": null, "endtime": "1970-01-01T10:28:04.000Z", "nextruntime": "2019-11-29T11:20:00.960Z", "createddate": null, "createdby": "deepankar", "updateddate": null, "updatedby": null, "ExcelSheetNames": "BaseData,Health,Term,Investment,Motor,EMAILBODY,EMAILBODY", "querysource": "{$query:{name:\"garvitjain\",count:{$lt:6}}}", "ccmail": null, "bccmail": null, "frommail": null }
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleShow = this.handleShow.bind(this);
    this.handleSave = this.handleSave.bind(this);
    this.productchange = this.productchange.bind(this);
    //this.GroupNamelistchange = this.GroupNamelistchange.bind(this);
    //this.agentranklistchange = this.agentranklistchange.bind(this);
    this.selectedrow = { "Id": 0, "ProductId": null, "IsRequired": true, "IsActive": true, "StartDate": new Date(), "EndDate": moment(new Date()).add(1, 'days'), "MultiLoginCount" : 1, "SurveyLocation": 'PreLogin' }
    const Cell = ({ v }) => (
      <span title={v}>{(v) ? v.substring(0, 25) : ''}{(v && v.length > 25) ? '...' : ''}</span>
    );
   
    //this.formColumnList = this.columnlist;
    this.ProductList = {
      config: {
        root: "ProductVideoUpload",
        data: [{ Id: 115, Display: "Investment" }, { Id: 2, Display: "Health" },{Id: 7 , Display: "TermLife"},{Id: 117, Display: "Motor"},{Id: 131, Display: "SME/GMC"}],
      }
    };
  }



  componentDidMount() {

    this.columnlist.map(col => (
      fnBindRootData(col, this.props)
    ));
    this.setState({IsLoading : true});
    this.props.GetCommonspData({
      root: 'UploadVideoData',
      c: "L",
      params: [{ "ProductID": 0 }],
    }, function (result) {
      if (result.data && result.data.data[0].length > 0) {
          this.setState({ VideoData: result.data.data[0] },
          () => this.updateFormColumnListForUser((getuser() && getuser().UserID)?getuser().UserID : 0)
        )
      }else{
        this.setState({IsLoading : false});
      }
    }.bind(this));

    setTimeout(function () {
      this.setState({ user: getuser() });
    }.bind(this), 500);

  }

  fnBindStore(col, nextProps) {
    if (col.type == "dropdown") {
      let items;
      if (nextProps.CommonData[this.state.root] && nextProps.CommonData[col.config.root]) {
        items = joinObject(this.state.VideoData, nextProps.CommonData[col.config.root], col.name)
        this.setState({ items: items });
      }
    }
  }

  componentWillReceiveProps(nextProps) {

    if (!nextProps.CommonData.isError) {
      this.setState({ items: nextProps.CommonData[this.state.root] });
      this.setState({ store: nextProps.CommonData });
      this.columnlist.map(col => (
        this.fnBindStore(col, nextProps)
      ));
    }



    if (nextProps.CommonData && nextProps.CommonData.InsertSuccessData && nextProps.CommonData.InsertSuccessData.status) {
      if (nextProps.CommonData.InsertSuccessData.status != 200) {
        //alert(nextProps.CommonData.InsertSuccessData.error);
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      }
      else {
        //this.setState({ showModal: false });
        this.setState({ showAlert: true, AlertMsg: "Record Add Successfully.", AlertVarient: "success" });
      }
    }

    if (nextProps.CommonData && nextProps.CommonData.UpdateSuccessData && nextProps.CommonData.UpdateSuccessData.status) {
      if (nextProps.CommonData.UpdateSuccessData.status != 200)
        this.setState({ showAlert: true, AlertMsg: nextProps.CommonData.InsertSuccessData.error, AlertVarient: "danger" });
      else {
        //this.setState({ showModal: false });
        this.setState({ showAlert: true, AlertMsg: "Record Updated Successfully.", AlertVarient: "success" });
      }
    }

  }

  validURL(str) {
    var pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
      '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
      '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
      '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
      '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
      '(\\#[-a-z\\d_]*)?$', 'i'); // fragment locator
    return !!pattern.test(str);
  }

  getHtmlListen(e, row) {//debugger;
    this.setState({ clickVideo: true, clickModalUrl: row.PageHtmlUrl, content: row.ContentType });
    if (row.ContentType == 2) {
      var video = document.getElementById('SurveyVideo');
      if (video) {
        video.play();
      }
    }
  }

  fnDatatableCol() {
    const columnslist = this.columnlist;//.filter(task => task.label !== 'Enter Link Url');


    var columns = fnDatatableCol(columnslist);

    // columns.push({
    //   name: "Products Name",
    //   selector: "ProductID_display",
    //   sortable: true,
    // });

    columns.push({
      name: "Action",
      cell: row => <ButtonGroup aria-label="Basic example">
        <Button variant="secondary" onClick={() => this.handleCopy(row)}><i className="fa fa-files-o" aria-hidden="true"></i></Button>
        <Button variant="secondary" onClick={() => this.handleEdit(row)}><i className="fa fa-pencil-square-o" aria-hidden="true"></i></Button>
      </ButtonGroup>
    });
    return columns;
  }

  async handleCopy(row) {
    await this.props.GetCommonspData({
      root: 'UploadVideoData',
      c: "L",
      params: [{ "pageId": row.PageId }],
    }, function (result) {
      if (result.data && result.data.data[0] && result.data.data[0].length > 0) {
        this.CheckEnableDisableFields(row.SurveyLocation); 
        this.CheckContentType(row.SurveyLocation);
        this.updateFormColumnListForUser((getuser() && getuser().UserID)?getuser().UserID : 0);

        if (result.data.data[1] && result.data.data[1].length > 0) {
          if (result.data.data[1][0].RoleId == 0) {
          } else {
            let RoleIdArray = [];
            result.data.data[1].map(function (val) {

              RoleIdArray.push({
                "label": val.RoleName.toString(),
                "value": val.RoleId,
              });
            })
            row.RoleId = RoleIdArray;
          }
        }

        if (result.data.data[3] && result.data.data[3].length > 0) {
          row.ProductID = result.data.data[3][0].ProductID;
        }

        if (result.data.data[2].length > 0) {
          if (result.data.data[2][0].GroupId == 0) {
            if(row.SurveyLocation == 'PreLogin'){
            row.GroupId = [{ label: "All items are Selected", value: "*" }]
            }
          } else {
            let GroupIdArray = [];
            result.data.data[2].map(function (val) {

              GroupIdArray.push({
                "label": val.UserGroupName.toString(),
                "value": val.GroupId,
              });
            })
            row.GroupId = GroupIdArray;
          }
        }
        this.setState({ formvalue: row, event: "Copy", showModal: true, FormTitle: "Copy Record" }
      );
      }
    }.bind(this));

  }
  handleEdit(row) {
    this.updateFormColumnListForUser((getuser() && getuser().UserID)?getuser().UserID : 0);
    this.setState({ od: Object.assign({}, row, {}), formvalue: Object.assign({}, row, {}), event: "Edit", showModal: true, FormTitle: "Edit Record" });
  }
  handleClose() {
    this.setState({ showModal: false });
  }
  handleShow() {
    this.setState({ formvalue: this.selectedrow, event: "Add", showModal: true, FormTitle: "Add New Record" },
    () => {
      this.CheckEnableDisableFields(this.selectedrow.SurveyLocation); 
      this.CheckContentType(this.selectedrow.SurveyLocation);
      this.updateFormColumnListForUser((getuser() && getuser().UserID)?getuser().UserID : 0);
      if(!this.selectedrow.ProductID){
        document.getElementById('MultiLoginCount').disabled = true;
      }else{
        document.getElementById('MultiLoginCount').disabled = false;
      }
    }
    );
  }

  async handleSave(e) {
    e.preventDefault();

    if (document.getElementsByName("frmUploadVideo").length > 0 &&
      document.getElementsByName("frmUploadVideo")[0].reportValidity()) {

      let formvalue = JSON.parse(JSON.stringify(this.state.formvalue));
      this.fnCleanData(formvalue);
      if (!('InvTypeID' in formvalue)) {
        formvalue["InvTypeID"] = 1;
      }
      let id = formvalue["Id"];
      let by = getuser().UserID;

      delete formvalue["Id"]
      if (this.state.event == "Edit") {//debugger;
        
        this.fnCleanData(formvalue);
        this.setState({ selectedFile: '' });
        var result = await this.addVideo(formvalue, "edit");
        if (result == false) {
          return false;
        }

      } else if (this.state.event == "Copy") {
        this.fnCleanData(formvalue);
        var result = await this.addVideo(formvalue, "add");
        if (result == false) {
          return false;
        }

      } else {

        var result = await this.addVideo(formvalue, "add");
        if (result == false) {
          return false;
        }

      }
      let productid = parseInt(formvalue["ProductID"]);
      let pageid = (parseInt(formvalue["PageId"]))?formvalue["PageId"] : null;
      if (productid) {
        var con = { "ProductId": productid };
      } else {
        var con = { "pageId": pageid };
      }

      //setTimeout(function () {

      if (productid || pageid) {//debugger;
        this.props.GetCommonspData({
          root: 'UploadVideoData',
          c: "L",
          params: [con],
        }, function (result) {
          if (result.data && result.data.data[0]) {//debugger;
            this.setState({ VideoData: result.data.data[0] });
          }
        }.bind(this));
      }

      // }.bind(this), 2000);
      this.setState({ showModal: false });
    }
    this.setState({ addClass: 'btn btn-primary' })
    return false;
  }

  async addVideo(formvalue, event) {
    // if (formvalue["GroupId"] == '' || typeof formvalue["GroupId"] == "undefined") {
    //   alert("Please enter group");
    //   return false;
    //     } 
    if (event == "add") {
      if ((formvalue["RoleId"] == '' || typeof formvalue["RoleId"] == "undefined") && formvalue["SurveyLocation"] != 'Login') {
        alert("Please enter Role");
        return false;
      }
      // if (formvalue["ProductID"] == '' || typeof formvalue["ProductID"]  == "undefined") {
      // alert("Please enter Product");
      // return false;
      // } 
      if (formvalue["SurveyName"] == '' || typeof formvalue["SurveyName"] == "undefined") {
        alert("Please enter Video/Image Title");
        return false;
      }


    }
    if (Object.keys(this.state.errors).length > 0) {
      alert("Form not Submitted");
      return false;
    }
    var newItem = document.createElement('span');
    newItem.setAttribute("class", "UploadVideoSave fa fa-spin fa-spinner");
    newItem.setAttribute("id", "UploadVideoSpan");

    document.getElementById('saveVideoForm').insertBefore(newItem, document.getElementById('saveVideo'));

    let by = getuser().UserID;

    // Create an object of formData 
    const formData = new FormData();
    // Update the formData object 
    if (event == "add") {
      formData.append(
        "myFile",
        this.state.selectedFile,
        this.state.selectedFile.name
      );
    }
    if (event == "add") {
      if (formvalue["RoleId"] && Array.isArray(formvalue["RoleId"])) {
        var roleIds = formvalue["RoleId"].map(function (val) {
          // if (val.value == '*')
          //   return 0;
          // else
            return val.value;
        });
        var selectedroles = roleIds.join(",");
      } 
      // else {
      //   var selectedroles = 0;
      // }
      if (Array.isArray(formvalue["GroupId"])) {
        var groupIds = formvalue["GroupId"].map(function (val) {
          if (val.value == '*')
            return 0;
          else
            return val.value;
        });
        var selectedgroups = groupIds.join(",");
      } else {
        var selectedgroups = 0;
      }
    }
    if(formvalue["SurveyLocation"] === 'Login'){
      selectedgroups = '';
      selectedroles = '';
      formvalue["ProductID"] = '';
    }
    if (event == "add") {
      if (formvalue["ProductID"]) {
        formData.append('ProductId', formvalue["ProductID"]);
      } else {
        formData.append('ProductId', '');
      }
      formData.append('roleId', selectedroles);
      formData.append('groupId', selectedgroups);
    }
    if (event == "edit") {
      formData.append('pageId', formvalue["PageId"]);
    }
    formData.append('StartDate', formvalue["StartDate"]);
    formData.append('Endtime', formvalue["EndDate"]);
    formData.append('SurveyName', formvalue["SurveyName"]);
    formData.append('IsRequired', formvalue["IsRequired"]);
    formData.append('IsActive', formvalue["IsActive"]);
    formData.append('userId', by);
    formData.append('ContentType', formvalue["ContentType"]);
    formData.append('SurveyLocation', formvalue["SurveyLocation"]);
    if(formvalue["MultiLoginCount"]){
    formData.append('MultiLoginCount', formvalue["MultiLoginCount"]);
    }else{
    formData.append('MultiLoginCount', 1); 
    } 
    if(formvalue["Link"] && formvalue["ContentType"] == 4){
    formData.append('Link', formvalue["Link"]);  
    }else{
      formData.append('Link', '');   
    }

    let resultsop = await PostUploadVideoFormData(formData);
    if(resultsop && resultsop.data && resultsop.data.data && resultsop.data.data[0] 
      && resultsop.data.data[0].status == 0
    ){
       
          alert('MultiLogin is allowed once per product');
          var newItem = document.getElementById("UploadVideoSpan");
          if (newItem) {
            newItem.remove();
          }
          //newItem.classList.remove("fa", "fa-spin", "fa-spinner");
          return false;
         
    }
    //  function (results) {
    //this.setState({ pageId: results.data.data[0].pageId });
    if (event == "add") {
      alert('Record Added');
    } else if (event == "edit") {
      alert('Record Updated');
    }
    // }.bind(this));

  }

  getExtension(filename) {
    var parts = filename.split('.');
    return parts[parts.length - 1];
  }

  isVideo(filename) {
    var ext = this.getExtension(filename);
    switch (ext.toLowerCase()) {
      case 'm4v':
      case 'avi':
      case 'mpg':
      case 'mp4':
      case 'webm':
      case 'mov':
        // etc
        return true;
      default:
        return false;  
    }
  }

  isImage(filename) {
    var ext = this.getExtension(filename);
    switch (ext.toLowerCase()) {
      case 'gif':
      case 'jpeg':
      case 'png':
      case 'jpg':
        // etc
        return true;
      default:
        return false;  
    }
  }

  bytesToMegaBytes(bytes) { //debugger;
    return bytes / (1024 * 1024);
  }

  handleChange = (e, props) => {

    let formvalue = this.state.formvalue;
    let od = this.state.od;

    if(e.target && e.target.id == "ProductID" ){
      if( e.target.value == ""){
      document.getElementById('MultiLoginCount').value = 0;
      formvalue['MultiLoginCount'] = 1;
      document.getElementById('MultiLoginCount').disabled = true;
      }else{
      document.getElementById('MultiLoginCount').disabled = false;
      }
    }

    if(e.target && e.target.id == "SurveyLocation" ){
      this.CheckEnableDisableFields(e.target.value); 
      this.CheckContentType(e.target.value);
    }

    if (e.target && e.target.type == "select-one" && e.target.id == "ContentType") {
      formvalue[e.target.id] = e.target.value;
      this.setState({ selectedFile: null, errors: {} });
      var el = document.getElementById('Video');
      el.value = null;


      if (e.target.value == 4) {
        document.getElementById("Link").style.display = "block";
        document.querySelector('label[for="Link"]').style.display = "block";
      } else {
        document.getElementById("Link").style.display = "none";
        document.querySelector('label[for="Link"]').style.display = "none";
      }
    } else if (e.target && e.target.id == "Link") {
      formvalue[e.target.id] = e.target.value;

      if (e.target.value && !this.validURL(e.target.value)) {

        this.setState({ errors: Object.assign({}, this.state.errors, { Link: "Select Valid Link Url" }) })
      } else {
        this.setState({ errors: _.omit(this.state.errors, 'Link') });
        //setTimeout(function(){  console.log('after', this.state.errors);}.bind(this),500);   

      }

    }
    else if (e.target && e.target.type == "checkbox") {
      formvalue[e.target.id] = e.target.checked;
    }
    else if (e.target && e.target.type == "file") {
      if (e.target.files[0]) {
        var content = document.getElementById('ContentType').value;

        if (typeof content == "undefined" || content == null || content == '') {
          this.setState({ errors: Object.assign({}, this.state.errors, { video: "Select Content Type First" }) })
        } else if (content == 2 && ((!this.isVideo(e.target.files[0].name)) || this.bytesToMegaBytes(e.target.files[0].size) > 25)) {
          this.setState({ errors: Object.assign({}, this.state.errors, { video: "Please select a valid video file of size up to 25MB." }) })

        } else if (content == 4 && (!this.isImage(e.target.files[0].name))) {
          this.setState({ errors: Object.assign({}, this.state.errors, { video: "Please select a valid image file" }) })
        }
        else {
          this.setState({ errors: _.omit(this.state.errors, 'video') })
        }
        this.setState({ selectedFile: e.target.files[0] });
      }
    }
    else if (e && e.type == "multiselect") {
      //this.setState({ selectedFile: e.target.files[0] }); 
      //debugger;
      // if (e.selectAll == true) {
      //   formvalue[e.id] = 5;
      // } else {
        formvalue[e.id] = e.newselectedList;
      //}
    }
    else if (e._isAMomentObject) {
      formvalue[props] = e.format()
    }
    else {
      formvalue[e.target.id] = e.target.value;
    }
    //debugger;
    //let Changed = CompareJson(od, formvalue);

    this.setState({ formvalue: formvalue, ModalValueChanged: true });
  }

  updateFormColumnListForUser = (userId) => {
    let LoginBannerUploadUserIds = [];
    const updates = {};
    let filteredData = [];
    this.props.GetCommonData({
      root: 'Settings',
      c: "MATRIX_DASHBOARD_CLIENT",
      con: JSON.stringify({ key: 'UploadLoginBannerUsers' })
    }, (result) => { 
      if (result?.data?.data && Array.isArray(result?.data?.data) && result.data.data.length > 0) {
        LoginBannerUploadUserIds =  result.data.data[1].data;
        if(LoginBannerUploadUserIds.includes(parseInt(userId))){
          filteredData = this.state.VideoData;
        }else{
         filteredData = Array.isArray(this.state.VideoData) && this.state.VideoData.filter(
          (item) =>  item.SurveyLocation && item.SurveyLocation.toLowerCase() === "prelogin"
        );
        }
        this.setState({ VideoData: filteredData , IsLoading : false});
        // Check if the user ID matches the specific ID
      if (LoginBannerUploadUserIds.includes(parseInt(userId))) {
        updates["SurveyLocation"] = {
          config: {
            root: "SurveyLocationLogin",
            data: [{ Id: 'Login', Display: 'Login' }],
            firstoption: "PreLogin",
            firstoptionvalue: "PreLogin",
          },
        };
          // Call the common update function
        this.updateFormColumnList(updates);
      }   
      }
    });
        
  };

  updateFormColumnList = (updates) => {
    this.setState((prevState) => ({
      formColumnList: prevState.formColumnList.map((column) => {
        const update = updates[column.name];
        if (update) {
          // Apply the updates for the column
          return {
            ...column,
            ...update,
          };

        }
        return column; // Return column as is if no update is specified
      }),
    }));
    
  };

  CheckEnableDisableFields = (Location) => {
    const isDisabled = Location === "Login";
    this.updateFormColumnList({
      ProductID: { disabled: isDisabled },
      RoleId: { disabled: isDisabled },
      GroupId: { disabled: isDisabled },
      MultiLoginCount: { disabled: isDisabled },
      Link : { disabled: isDisabled }
    });
  };

  CheckContentType = (Location) => {
    const contentTypeData =
      Location === "Login"
        ? [{ Id: 4, Display: "Image" }]
        : [
            { Id: 4, Display: "Image" },
            { Id: 2, Display: "Video" },
          ];
  
    this.updateFormColumnList({
      ContentType: {
        config: {
          ...this.state.formColumnList.find((col) => col.name === "ContentType").config,
          data: contentTypeData,
        },
      },
    });
  };


  fnCleanData(formvalue) {
    formvalue = fnCleanData(this.columnlist, formvalue);
    this.setState({ formvalue: formvalue });
  }
  productchange(e, props) {
    // setTimeout(function () {
    this.props.GetCommonspData({
      root: 'UploadVideoData',
      c: "L",
      params: [{ "ProductId": e.target.value }],
    }, function (result) {
      if (result.data && result.data.data[0]) {
        this.setState({ VideoData: result.data.data[0] });
      }
    }.bind(this));
  }

  fnRenderfrmControl(col, formvalue, handleChange, event, errors) {
    setTimeout(() => {

      if (formvalue && formvalue['ContentType'] == 4) {
        if(document.getElementById("Link")){
        document.getElementById("Link").style.display = "block";
        document.querySelector('label[for="Link"]').style.display = "block";

        }
      } else {
        if(document.getElementById("Link")){
        document.getElementById("Link").style.display = "none";
        document.querySelector('label[for="Link"]').style.display = "none";

        }
      }

    }, 200);


    return fnRenderfrmControl(col, formvalue, handleChange, event, errors)

  }

  renderViewData(ContentType, clickModalUrl) {
    if (ContentType == 4) {
      return <img src={clickModalUrl} />
    } else if (ContentType == 2) {
      return <video id="SurveyVideo" width="450" controls>
        <source src={clickModalUrl} />
      </video>
    }
  }



  render() {
    const columns = this.fnDatatableCol();
    const { PageTitle, showModal, FormTitle, formvalue, errors, showAlert, AlertMsg, AlertVarient, ModalValueChanged, event, content } = this.state;
    return (
      <>
        <div className="content">
          <AlertBox show={showAlert} variant={AlertVarient} body={AlertMsg}></AlertBox>
          <Row>
            <Col md="12">
              <Card>
                <CardHeader>
                  <Row>
                    <Col md={7}>
                      <CardTitle tag="h4">{PageTitle}</CardTitle>
                    </Col>
                    <Col md={3}>
                      <Form.Group as={Col} md={12} controlId="product_dropdown">
                        <DropDown firstoption="Select Product" col={this.ProductList} onChange={this.productchange}>
                        </DropDown>
                      </Form.Group>
                    </Col>
                    <Col md={2}>
                      <Button variant="primary" onClick={this.handleShow}>ADD</Button>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                {this.state.IsLoading ? <div className="spinner-container"><i class="fa fa-spinner fa-spin"></i></div> : ''}
                {!this.state.IsLoading && <DataTable
                    columns={columns}
                    data={this.state.VideoData}
                  />}
                </CardBody>
              </Card>
            </Col>
          </Row>

          <Modal show={showModal} onHide={this.handleClose} dialogClassName="modal-90w">
            <Modal.Header closeButton>
              <Modal.Title>{FormTitle}</Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <Form name="frmUploadVideo">
                <Row key={JSON.stringify(this.state.formColumnList)}>
                  {this.state.formColumnList.map(col => (
                    this.fnRenderfrmControl(col, formvalue, this.handleChange, event, errors)
                  ))}
                </Row>
              </Form>
            </Modal.Body>
            <Modal.Footer>
              <Button variant="secondary" onClick={this.handleClose}>
                Close
              </Button>
              <If condition={ModalValueChanged}>
                <Then>
                  <form ref="form" onSubmit={this.handleSave} id="saveVideoForm">
                    <input type="submit" id="saveVideo" className='btn btn-primary btnsaveVideo' value="Save Changes" />
                  </form>
                </Then>
              </If>
            </Modal.Footer>
          </Modal>

          <Modal show={this.state.clickVideo} onHide={() => this.setState({ clickVideo: false })} dialogClassName="modal-50w">
            <Modal.Header closeButton>
              <Modal.Title></Modal.Title>
            </Modal.Header>
            <Modal.Body>
              <div className="modalmoreinfodata">
                {this.renderViewData(content, this.state.clickModalUrl)}
              </div>
            </Modal.Body>
            <Modal.Footer>

            </Modal.Footer>
          </Modal>

        </div>
      </>
    );
  }
}


function mapStateToProps(state) {
  return {
    CommonData: state.CommonData
  };
}

export default connect(
  mapStateToProps,
  {
    GetCommonData,
    InsertData,
    UpdateData,
    addRecord,
    GetCommonspData
  }
)(UploadVideo);