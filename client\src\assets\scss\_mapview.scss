* {
    font-family: '<PERSON><PERSON>', sans-serif !important;
}

.MuiDrawer-paper {
    width: 100%;
    position: relative;
    overflow: hidden !important;

}

ul.main-listing.map-listing {
    overflow-x: scroll;
    // width: 540px;
    width: 40.5%;
    margin-bottom: 0px;


    li:not(:first-child) {
        padding-right: 57px;
        white-space: nowrap;
        margin: 0;
    }

}

.HeightChange {
    height: calc(100vh - 260px) !important;
}

.scroll-listing {
    overflow-y: auto;
    height: calc(100vh - 170px);
    width: 79%;
    scrollbar-width: thin;
    //    height: 630px;

    //    @media(max-width: 1367px){
    //       height: 480px;
    //    }

    //    @media(max-width: 1280px){
    //     height: 410px;
    //    }

    &::-webkit-scrollbar,
    &::-webkit-scrollbar-thumb,
    &::-webkit-scrollbar-track {
        width: 0px;
        background: transparent;
        box-shadow: none;
        border-radius: 0;
        margin-right: 0;
        color: inherit;
    }

}

.MuiGrid-container {
    margin: 0 !important;
    width: 100% !important;
    flex-direction: column !important;

    .MuiGrid-item {
        padding: 12px 0px 0px !important;
        // max-width: 100% !important;
        transition: all 400ms ease;
        -webkit-transition: all 400ms ease;
        -moz-transition: all 400ms ease;

        .MuiPaper-elevation {
            border-radius: 4px;
            background: #F9F9F9;
            padding: 16px;
            text-align: left;
            box-shadow: none;
            height: auto;

            .left-content-container {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                align-items: center;
                border: 2px solid transparent;
                cursor: pointer;
                transition: all 400ms ease;
                -webkit-transition: all 400ms ease;
                -moz-transition: all 400ms ease;
            }

            &.active {
                border-color: #0065FF;
                pointer-events: none;
                // animation: slideHeight 400ms ease;
                // -webkit-animation: slideHeight 400ms ease;
                // -moz-animation: slideHeight 400ms ease;
                height: auto;
                opacity: 1;

                // @keyframes slideHeight {
                //     0%{
                //         height: 81px;
                //         opacity: 0;
                //     }
                //     100%{
                //     height: 151px;
                //     opacity: 1;
                //     }
                // }

                &.meeting {
                    border-color: #AF5F14;
                    // animation: meetingslideHeight 400ms ease;
                    // -webkit-animation: meetingslideHeight 400ms ease;
                    // -moz-animation: meetingslideHeight 400ms ease;

                    // @keyframes meetingslideHeight {
                    //     0%{
                    //         height: 81px;
                    //         opacity: 0;
                    //     }
                    //     100%{
                    //     height: 199px;
                    //     opacity: 1;
                    //     }
                    // }

                }

                &.bound {
                    border-color: #E44A4A;
                }
            }

            h5 {
                color: #253858E3;
                font-size: 18px;
                font-weight: 500;
                margin-bottom: 0px;
            }

            span {
                font-size: 13px;
                font-weight: 400;
                color: #253858E3;

                small {
                    font-size: 13px;
                    color: #25385899;
                    font-weight: 500;
                    position: relative;
                    margin-left: 4px;
                    padding-left: 8px;
                    vertical-align: middle;

                    &::before {
                        position: absolute;
                        content: '';
                        top: 0;
                        bottom: 0;
                        left: 0;
                        background: #0000001F;
                        width: 1px;
                        height: 15px;
                        margin: auto;
                    }
                }

                &.status {
                    color: #0065FF;
                    font-size: 12px;
                    font-weight: 400;
                    font-style: italic;
                    display: flex;
                    align-items: center;

                    &.meeting {
                        color: #AF5F14;
                    }

                    &.bound {
                        color: #E44A4A;
                    }

                    &.loggedOff {
                        color: #E44A4A;
                    }

                    &.lunch {
                        color: #CB2AD7;
                    }

                    img {
                        padding-left: 8px;
                    }
                }

            }

            .innerContent {
                opacity: 0;
                height: 0;
                display: none;

                &.show {
                    opacity: 1;
                    height: 62px;
                    margin-top: 8px;
                    width: 100%;
                    display: block !important;
                }

                .advisor-text {
                    background: #FFEFD6;
                    border-radius: 3px;
                    display: flex;
                    gap: 5px;
                    align-items: center;
                    padding: 8px 16px;
                    color: #AF5F14;
                    font-size: 14px;
                    font-weight: 500;
                    margin-bottom: 8px;
                }

                .inner-list {
                    border-radius: 3px;
                    background: #F3F3F3;
                    padding: 8px 14px;

                    ul {
                        padding: 0;
                        margin: 0;
                        border: 0;
                        display: flex;
                        gap: 18px;
                        align-items: center;
                        justify-content: space-between;

                        li {
                            background: transparent;
                            box-shadow: none;
                            padding: 0;
                            max-width: inherit;
                            border-radius: 0;
                            pointer-events: none;
                            white-space: nowrap;

                            label {
                                font-size: 14px;
                                color: #25385899;
                                font-weight: 600;
                                display: block;
                                line-height: normal;
                                margin-bottom: 5px;
                            }

                            span {
                                font-size: 16px;
                                font-weight: 500;
                                color: #253858E3;
                            }
                        }
                    }

                    &.null span {
                        color: #25385861;
                    }
                }
            }

            &.meeting .show {
                height: 110px;
            }


        }

    }

}

.ryt-side {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    z-index: 5;
    width: 59%;
    // height: 500px;

    &.fullDetailview {
        width: 80%;
        border-left: 2px solid #fff;
        box-shadow: 0px 3px 12px 0px #00000070;

    }


    .mapscreen .dvmapview {
        width: 100% !important;

        // @media(max-width: 1430px){
        //     width: 910px !important;
        // }

        // @media(max-width: 1370px){
        //     width: 841px !important;
        //     // width: 1370px !important;
        // }

        // @media(max-width: 1306px){
        //     width: 780px !important;
        //     // width: 1370px !important;
        // }

        // @media(max-width: 1280px){
        //     width: 755px !important;
        //     // width: 1280px !important;
        // }

    }

    // .fullview .dvmapview{
    //     width: 1370px !important;

    //     @media(max-width: 1430px){
    //         width: 1430px !important;
    //     }

    //     @media(max-width: 1470px){
    //         width: 1470px !important;
    //     }

    // }







    // .journeyBox div{
    //     height: 500px;
    // }

}

button.gm-fullscreen-control,
button.gm-svpc,
.gm-style-mtc {
    // top: 50% !important;
    // transform: translate(0%, -50%) !important;
    // -webkit-transform: translate(0%, -50%) !important
    display: none !important;
}

.cxLocationlabel {
    color: #fff !important;
    font-weight: 500 !important;
    font-size: 10px !important;
    margin: 0 !important;
    //  background: #d34142 !important;
    background: #d98a1a !important;
    width: auto !important;
    padding: 8px;
    border-radius: 20px;
    box-shadow: 0px 3px 8px rgba(0, 101, 255, 0.1607843137);

}

.AgLocationlabel {
    color: #fff !important;
    font-weight: 400 !important;
    font-size: 8px !important;
    margin: 0 !important;
    background: #004af2 !important;
    width: auto !important;
    padding: 8px;
    border-radius: 20px;
    border: 1px solid #FFF;
    box-shadow: 0px 3px 8px rgba(0, 101, 255, 0.1607843137);

}

.DoneCXLocation {
    color: #fff !important;
    font-weight: 500 !important;
    font-size: 10px !important;
    margin: 0 !important;
    background: #2c863f !important;
    width: auto !important;
    padding: 8px;
    border-radius: 20px;
    box-shadow: 0px 3px 8px rgba(0, 101, 255, 0.1607843137);

}

.CXLocation {
    color: #fff !important;
    font-weight: 500 !important;
    font-size: 10px !important;
    margin: 0 !important;
    background: #d34142 !important;
    //  background: #d98a1a !important;
    width: auto !important;
    padding: 8px;
    border-radius: 20px;
    box-shadow: 0px 3px 8px rgba(0, 101, 255, 0.1607843137);

}

.time-box {
    position: fixed;
    top: 5px;
    left: 13px;
    width: 53px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #000;
    border-radius: 5px;
    background-color: #f0f0f0;
    font-size: 10px;
    font-family: Arial, sans-serif;
    color: #333;
    z-index: 1000;
}

.AdvisorMsg {
    background-color: #FFEFD6;
    color: #AF5F14;
    padding: 12px 10px;
    display: flex;
    font-size: 14px;
    font-weight: 500;
    line-height: 19px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    width: 100%;
    align-items: baseline;
    gap: 8px;
    border-radius: 3px;
}

.WeekOff {
    font-family: Roboto;
    font-size: 12px;
    font-weight: 600;
    line-height: 14.06px;
    text-align: left;
    background-color: #AF5F141A;
    color: #AF5F14;
    border-radius: 8px;
    margin-bottom: 5px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 96px;
}

.AppoitmentsButton {

    background-color: #C9F1E8;
    font-family: Roboto;
    font-size: 12px;
    font-weight: 500;
    line-height: 12.75px;
    text-align: left;
    text-transform: capitalize;
    color: #3B6259;
    border: none;
    border-radius: 24px;
    margin-left: 120px;
    display: flex;
    align-items: center;
    padding: 6px 17px;
    width: auto;

    svg {
        font-size: 17px;
        margin-left: 4px;
    }

    &:hover {
        background-color: #C9F1E8 !important;
        color: #3B6259 !important;
    }
}

.AppoitmentCardTooltip {
    padding: 0px;
    box-shadow: 0px 4px 12px 0px #00000040 !important;
    border-radius: 4px !important;
    width: 250px;

    .Header {
        background-color: #F3F7FF;
        padding: 8px 10px;
        border-radius: 4px 4px 8px 4px;

        h6 {
            font-size: 12px;
            font-weight: 500;
            line-height: 14.06px;
            text-align: left;
            text-transform: capitalize;
            color: #253858E3;
        }

        p {
            font-size: 9px;
            font-weight: 400;
            line-height: 13px;
            text-align: left;
            color: #25385899;
        }
    }



    .AppointmentDetails {
        padding: 0px 10px 10px 10px;
        // display: flex;
        // justify-content: space-between;
        max-height: 200px;
        overflow-y: auto;
        ;

        p {
            font-family: Roboto;
            font-size: 11px;
            font-weight: 500;
            line-height: 21px;
            text-align: left;
            color: #25385899;
        }

        h6 {
            font-family: Roboto;
            font-size: 13px;
            font-weight: 500;
            line-height: 15.23px;
            text-align: left;
            width: 100%;
            color: #253858;
            text-transform: capitalize;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
        }

        .MuiGrid-container {
            flex-direction: row !important;
        }

        hr {
            margin: 10px 0px 0px;
            background-color: #00000057;
            opacity: 0.3;
        }
    }

    .Travelling {
        background-color: #DDECFF;
        color: #0065FF;
        width: 190px;
        position: relative;
        padding: 4px 10px;
        border-radius: 3px;
        top: 8px;
        margin-left: 1px;
        font-size: 10px;
        font-weight: 500;
        line-height: 11.72px;
        text-align: left;
    }

    .WaitingCustomer {
        font-size: 10px;
        font-weight: 500;
        line-height: 11.72px;
        text-align: left;
        color: #477B82;
        position: relative;
        background-color: #02a0b633;
        width: 150px;
        padding: 4px 10px;
        border-radius: 3px;
        top: 8px;
        margin-left: 10px;
    }

    .Meeting {
        background-color: #f27c4933;
        color: #D9762A;
        width: 190px;
        position: relative;
        padding: 4px 10px;
        border-radius: 3px;
        top: 8px;
        margin-left: 10px;
        font-size: 10px;
        font-weight: 500;
        line-height: 11.72px;
        text-align: left;
    }
}

.Arrow {
    position: absolute;
    width: 0px;
    height: 0px;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-top: 8px solid white;
    bottom: -8px;
    left: 68%;

}

.InfoWindowData {
    max-width: 600px;
    width: 220px;
    color: black;
    font-weight: bold;
    padding: 0px;
    position: relative;


    .CloseButton {
        position: absolute;
        font-size: 18px;
        padding: 0px;
        top: 2px;
        display: block;
        min-width: 18px;
        margin: 0px;
        right: 0px;
        color: #253858;
        font-weight: bold;
        background-color: transparent !important;
        border: none;
        cursor: pointer;

        &:hover {
            background-color: transparent;
        }
    }

    .Key {
        font-size: 11px;
        font-weight: 500;
        color: #25385899;
        line-height: "21px";
    }

    .Value {
        font-size: 13px;
        font-weight: 500;
        color: #253858;
        text-transform: capitalize;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: "nowrap";
    }

    .Meeting {
        background-color: #f27c4933;
        color: #D9762A;
        width: 190px;
        position: relative;
        padding: 4px 10px;
        border-radius: 3px;
        top: 8px;
        margin-left: 1px;
        font-size: 10px;
        font-weight: 500;
        line-height: 11.72px;
        text-align: left;
    }

    .Travelling {
        background-color: #DDECFF;
        color: #0065FF;
        width: 190px;
        position: relative;
        padding: 4px 10px;
        border-radius: 3px;
        top: 8px;
        margin-left: 1px;
        font-size: 10px;
        font-weight: 500;
        line-height: 11.72px;
        text-align: left;
    }

    .WaitingCustomer {
        font-size: 10px;
        font-weight: 500;
        line-height: 11.72px;
        text-align: left;
        color: #477B82;
        position: relative;
        background-color: #02a0b633;
        width: 150px;
        padding: 4px 10px;
        border-radius: 3px;
        top: 8px;
        margin-left: 10px;
    }
}

.MapControl {
    position: absolute !important;
    z-index: 99999;
    box-shadow: 0px 3px 12px 0px #00000029;
    background-color: #fff;
    border: none;
    height: 40px;
    width: 140px;
    left: 45%;
    top: 6%;
    border-radius: 20px;

    .MuiOutlinedInput-root {
        border: none;
        font-family: Roboto;
        font-size: 14px;
        font-weight: 600;
        line-height: 20.41px;
        color: #253858E3;


        .MuiSelect-outlined {
            padding: 10px;
            text-align: center;
        }
    }

    fieldset {
        border: none;
    }
}

.OTSHierachyPopup {
    .MuiDialog-paperWidthSm {
        max-width: 796px !important;
        max-height: 642px !important;

        .MuiDialogContent-root {
            overflow: auto;
        }

        h2 {
            padding-bottom: 0px !important;

            h6 {
                font-family: Roboto;
                font-weight: 600;
                font-size: 24px;
                line-height: 100%;
                letter-spacing: 0px;
                color: #253858E3;
                text-transform: capitalize;
            }
        }

        .rct-collapse {
            padding: 0px;
        }

        .rct-title {
            padding: 0px;
        }

        .Team {
            font-family: Roboto;
            font-weight: 500;
            font-size: 16px;
            line-height: 100%;
            letter-spacing: 0px;
            color: #253858E3;
            margin-bottom: 15px;

        }

        .Score {
            margin: 1.7rem 0rem 1.5rem 0rem;

            h6 {
                font-family: Roboto;
                font-weight: 500;
                font-size: 16px;
                line-height: 100%;
                letter-spacing: 0.3px;
                color: #253858E3;
                text-transform: capitalize;
                display: flex;
                justify-content: space-between;
                margin-bottom: 0.7rem;
            }

            span {
                color: #253858E3;
                font-family: Roboto;
                font-weight: 600;
                font-size: 16px;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: right;

            }
        }

        .ProgressBar {
            height: 4px;
            border-radius: 16px;
            background-color: #0000001F;

            .MuiLinearProgress-bar {
                background-color: #E6A800;
            }
        }

        .HierachyTree {
            display: flex;
            align-items: center;

            p {
                font-family: Roboto;
                font-weight: 400;
                font-size: 16px;
                line-height: 100%;
                letter-spacing: 0px;
                color: #253858E3;
                text-transform: capitalize;
            }

            .Chip {
                background-color: #E6A800;
                border-radius: 4px;
                font-family: Roboto;
                width: 43px;
                height: 24px;
                font-weight: 400;
                font-size: 14px;
                line-height: 100%;
                letter-spacing: 0px;
                color: #fff;

            }



        }
    }

    .rct-node .rct-text label {
        display: flex;
        height: 38px;
        align-items: center;
        margin-bottom: 4px;

        &:hover {
            background-color: transparent;
        }

    }

    .scrollBar {
        height: 460px;
        overflow-y: auto;
    }
}

.ots-score-card {
    background: #F8F8F8;
    padding: 15px 15px;
    border-radius: 4px;
    margin-bottom: 10px;
    max-width: 79%;
    border: 1px solid #EBEBEB;
}

.ots-header {
    display: flex;
    justify-content: space-between;
    font-weight: 600;
    color: #253858E3;
    margin-bottom: 4px;
    font-size: 16px;
    line-height: 100%;


}

.ots-progress {
    width: 100%;
    height: 4px;
    border-radius: 16px;
    -webkit-appearance: none;
    appearance: none;
}

.ots-progress::-webkit-progress-bar {
    background: #0000001F;
    border-radius: 4px;
}

.ots-progress::-webkit-progress-value {
    background: #E6A800;
    border-radius: 4px;
    transition: width 0.5s ease-in-out;
}

.ots-progress::-moz-progress-bar {
    background: #E6A800;
    border-radius: 4px;
}

.ots-link {
    text-decoration: underline;
    color: #253858E3;
    display: block;
    margin-top: 8px;
    font-weight: 600;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0.3px;
    text-align: right;
    cursor: pointer;

    &:hover {
        color: #0065FF;
    }

}

// Next Visit at Risk Component Styles
.next-visit-risk {
    background: #FFF3E0;
    border: 1px solid #FFE0B2;
    border-radius: 8px;
    padding: 16px;
    margin-top: 12px;
    font-family: 'Roboto', sans-serif;
    transition: all 0.3s ease;

    .risk-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
        padding-bottom: 8px;
        border-bottom: 1px solid #FFE0B2;

        .risk-icon {
            font-size: 16px;
            margin-right: 8px;
        }

        .risk-title {
            font-weight: 600;
            font-size: 16px;
            color: #E65100;
            flex: 1;
        }

        .details-btn {
            background: #FFB74D;
            border: none;
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 12px;
            font-weight: 500;
            color: #E65100;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s ease;

            &:hover {
                background: #FFA726;
                transform: translateY(-1px);
            }

            &:active {
                transform: translateY(0px);
            }
        }
    }

    .visits-section {
        margin-bottom: 16px;
        animation: fadeInDown 0.3s ease;

        &:last-child {
            margin-bottom: 0;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .visit-date {
            font-weight: 600;
            font-size: 14px;
            color: #424242;
            margin-bottom: 12px;

            sup {
                font-size: 10px;
                vertical-align: super;
            }
        }

        .visit-entries {
            .visit-entry {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
                padding: 8px 0;
                border-bottom: 1px solid #F5F5F5;

                &:last-child {
                    border-bottom: none;
                }

                .visit-left {
                    flex: 1;

                    .lead-id, .customer-name {
                        margin-bottom: 4px;

                        &:last-child {
                            margin-bottom: 0;
                        }

                        .label {
                            font-size: 11px;
                            color: #757575;
                            font-weight: 400;
                            display: block;
                            margin-bottom: 2px;
                        }

                        .value {
                            font-size: 13px;
                            color: #212121;
                            font-weight: 500;
                            display: block;
                        }
                    }
                }

                .visit-time {
                    text-align: right;
                    min-width: 80px;

                    .label {
                        font-size: 11px;
                        color: #757575;
                        font-weight: 400;
                        display: block;
                        margin-bottom: 2px;
                    }

                    .value {
                        font-size: 13px;
                        color: #212121;
                        font-weight: 600;
                        display: block;
                    }
                }
            }
        }
    }
}