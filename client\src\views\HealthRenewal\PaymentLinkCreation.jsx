import React, { useEffect, useState } from "react";
import { FetchHealthRenewalPaymentLinkService, CreateRenewalManualPaymentLinkService, FetchAgentPredefinedUrl } from "../../store/actions/CommonAction";
import {
    Card,
    CardBody,
    Row,
    Col
} from "reactstrap";
import { Button, Form, ButtonGroup } from 'react-bootstrap';
import dayjs from 'dayjs';
import DropDown from "views/Common/DropDown";
import { toast } from 'react-toastify';

const PaymentLinkCreation = (props) => {
    const { LeadId, Id, onClose } = props;
    let [data, setData] = useState({});
    let [linkcreated, setlinkcreated] = useState(false);

    const PaymentTypeList = [
        { Id: 1, Display: "Normal_Payment" },
        { Id: 2, Display: "Renewal_Payment" },
        { Id: 3, Display: "Medical_Payment" },
        { Id: 4, Display: "Loading_Payment" },
        { Id: 5, Display: "RE_PAYMENT" },
        { Id: 6, Display: "REFUND_PAYMENT" },
        { Id: 7, Display: "RENEWAL_LIFE" },
        { Id: 8, Display: "SI_RECURRING" },
        { Id: 9, Display: "HEALTH_EX" },
        { Id: 10, Display: "Swap_Payment" },
        { Id: 11, Display: "SPLIT_PAYMENT" },
        { Id: 12, Display: "BUNDLE_PAYMENT" },
        { Id: 13, Display: "ENDOSREMENT_PAYMEN" }
    ]

    useEffect(() => {
        if (LeadId > 0) {
            FetchHealthRenewalPaymentLinkService({ ID: Id, Type: 6 }, function (resultData) {
                if (resultData && resultData.data.data && Array.isArray(resultData.data.data[0])) {
                    let temp = {
                        ...resultData.data.data[0][0],
                        PaymentType: 0,
                        NSTP: "1",
                        HoldPayment: "0",
                        EMandateRegistration: "0",
                        IsEmailChanged: false
                    };

                    temp.PolicyExpiryDate = dayjs(temp.PolicyExpiryDate).format('DD/MM/YYYY');
                    temp.ProposalNo = temp.PolicyNo;
                    temp.OldEmailId = temp.EmailID;
                    temp.OldProposalNo = temp.ProposalNo;

                    setData(temp);
                }
            })
        }
    }, []);

    const PaymentTypeChange = (event) => {
        let temp = { ...data, PaymentType: event.target.value };

        setData(temp);
    }

    const handleFieldDataChange = (event, type) => {
        let temp = data;
        switch (type) {
            case "ProposalNo":
                if ((event.target.value).trim() == '') {
                    temp = { ...temp, ProposalNo: temp.OldProposalNo };
                }
                else {
                    temp = { ...temp, ProposalNo: event.target.value };
                }
                break;
            case "EmailID":
                if ((event.target.value).trim() == '') {
                    temp = { ...temp, EmailID: temp.OldEmailId, IsEmailChanged: false }
                }
                else {
                    temp = { ...temp, EmailID: event.target.value, IsEmailChanged: true };
                }
                break;
            default:
                break;
        }
        console.log(temp);
        setData(temp);
    }

    const RadioHandleChange = (e, type) => {
        let temp = {};
        if (type == "NSTP") {
            temp = { ...data, NSTP: e.target.value };
        }
        if (type == "EMandateRegistration") {
            temp = { ...data, EMandateRegistration: e.target.value };
        }
        if (type == "HoldPayment") {
            temp = { ...data, HoldPayment: e.target.value };
        }

        setData(temp);
    }

    const handleLinkCreate = () => {
        if (data.IsEmailChanged && data.IsEmailChanged == true) {
            if (data.EmailID != "" && data.EmailID != "undefined" & data.EmailID != null) {
                var pat1 = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                if (!pat1.test(data.EmailID.trim())) {
                    toast("Please enter correct Email ID.", { type: 'error' });
                    return;
                }
            }
            else {
                toast("Email Id can not be empty.", { type: 'error' });
                return;
            }
        }

        // link create 
        var reqData =
        {
            "leadId": data.LeadID,
            "id": Id,
            "emailId": data.EmailID,
            "isEmailchanged": data.IsEmailChanged,
            "txTypeId": data.PaymentType,
            "nstp": data.NSTP == 1,
            "holdPayment": data.HoldPayment == 1,
            "emandate": data.EMandateRegistration == 1,
            "proposalNo": data.ProposalNo
        };
        CreateRenewalManualPaymentLinkService(reqData, function (resultData) {
            if (resultData && resultData.data) {
                toast('Link created', { type: 'success' });
                setlinkcreated(true)
            }
            else{
                toast('Issue occured', { type: 'error' });
            }
        })
    }

    const handleViewAttachedFile = () => {
        FetchAgentPredefinedUrl(data.Id, function (resultData) {
            try {
                if (resultData != null && resultData.data.data) {
                    ViewAgentFile(resultData.data.data);
                }
            }
            catch (e) {
                toast(`${e}`, { type: 'error' });
            }
        });
    }

    const ViewAgentFile = (filepath) => {
        if (filepath && filepath != "")
            window.open(filepath)
        else
            toast(`Corrupted File!`, { type: 'error' });
    }

    return (
        <div>
            <Row>
                <Col md="12">
                    <Form>
                        <Card>
                            <CardBody>
                                <Row>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Supplier Name</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.InsurerName ? data.InsurerName : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Plan Name</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.PlanName ? data.PlanName : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Amount (Notice Premium)</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.PremiumAmount ? data.PremiumAmount : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark"> Name</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.Name ? data.Name : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <br />
                                <Row>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Customer EmailId</Form.Label>
                                            <Form.Control
                                                type="text"
                                                onChange={(e) => handleFieldDataChange(e, "EmailID")}
                                                placeholder={data.IsEmailChanged == true ? data.EmailID : data.OldEmailId}
                                                onKeyDown={(e) => {
                                                    if (e.key === " " && e.target.value.length === 0) {
                                                        e.preventDefault(); // prevent space at beginning
                                                    }
                                                }}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Enquiry ID</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.EnquiryNo ? data.EnquiryNo : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Proposal No</Form.Label>
                                            <Form.Control
                                                type="text"
                                                onChange={(e) => handleFieldDataChange(e, "ProposalNo")}
                                                placeholder={data.ProposalNo == data.OldProposalNo ? data.OldProposalNo : data.ProposalNo}
                                                onKeyDown={(e) => {
                                                    if (e.key === " " && e.target.value.length === 0) {
                                                        e.preventDefault(); // prevent space at beginning
                                                    }
                                                }}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Lead Source</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.LeadSource ? data.LeadSource : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <br />
                                <Row>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Policy No</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.PolicyNo ? data.PolicyNo : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Lead ID</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.LeadID ? data.LeadID : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={3}>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Reason of Payment</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.PaymentReason ? data.PaymentReason : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={3}>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Policy Expiry Date</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={data.PolicyExpiryDate ? data.PolicyExpiryDate : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <br />
                                <Row>
                                    <Col md={3}>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Payment Type</Form.Label>
                                            <DropDown firstoptionvalue={true} items={PaymentTypeList} onChange={PaymentTypeChange}>Feature</DropDown>
                                            {data.PaymentType != null && data.PaymentType != undefined && data.PaymentType == 0 ? <Form.Label className="text-danger">*Payment Type is manadatory</Form.Label> : <></>}
                                        </Form.Group>
                                    </Col>
                                    <Col md={3}>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">NSTP</Form.Label>
                                            <br />
                                            <Form.Check
                                                type="radio"
                                                label="Yes"
                                                name="NSTP"
                                                value={"1"}
                                                className="radio-online"
                                                checked={data.NSTP == "1"}
                                                onChange={(e) => RadioHandleChange(e, "NSTP")}
                                            />
                                            <Form.Check
                                                type="radio"
                                                label="No"
                                                name="NSTP"
                                                value={"0"}
                                                className="radio-online"
                                                checked={data.NSTP == "0"}
                                                onChange={(e) => RadioHandleChange(e, "NSTP")}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={3}>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Hold Payment</Form.Label>
                                            <br />
                                            <Form.Check
                                                type="radio"
                                                label="Yes"
                                                name="Hold Payment"
                                                value={"1"}
                                                className="radio-online"
                                                checked={data.HoldPayment == "1"}
                                                onChange={(e) => RadioHandleChange(e, "HoldPayment")}
                                            />
                                            <Form.Check
                                                type="radio"
                                                label="No"
                                                name="Hold Payment"
                                                value={"0"}
                                                className="radio-online"
                                                checked={data.HoldPayment == "0"}
                                                onChange={(e) => RadioHandleChange(e, "HoldPayment")}
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={3}>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">E-Mandate Registration</Form.Label>
                                            <br />
                                            <Form.Check
                                                type="radio"
                                                label="Yes"
                                                name="EMandateRegistration"
                                                value={"1"}
                                                className="radio-online"
                                                checked={data.EMandateRegistration == "1"}
                                                onChange={(e) => RadioHandleChange(e, "EMandateRegistration")}
                                            />
                                            <Form.Check
                                                type="radio"
                                                label="No"
                                                name="EMandateRegistration"
                                                value={"0"}
                                                className="radio-online"
                                                checked={data.EMandateRegistration == "0"}
                                                onChange={(e) => RadioHandleChange(e, "EMandateRegistration")}
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <br />
                                <Row>
                                    <Col>
                                        <div className="d-flex justify-content-center">
                                            {data.IsAgentAttachedFile == true ? <ButtonGroup>
                                                <Button
                                                    id="btnstaging"
                                                    className="btn btn-warning "
                                                    onClick={handleViewAttachedFile}
                                                >
                                                    View Attached File
                                                </Button>
                                                &nbsp;
                                            </ButtonGroup> : <></>}
                                            <ButtonGroup>
                                                <Button
                                                    id="btnstaging"
                                                    className="btn btn-success "
                                                    onClick={handleLinkCreate}
                                                    disabled={
                                                        (!(data.PaymentType != null && data.PaymentType != undefined && data.PaymentType != 0)) || linkcreated
                                                    }
                                                >
                                                    Create Link
                                                </Button>
                                            </ButtonGroup>
                                        </div>
                                    </Col>
                                </Row>
                            </CardBody>
                        </Card>
                    </Form>
                </Col>
            </Row>
        </div>
    )
}

export default PaymentLinkCreation;