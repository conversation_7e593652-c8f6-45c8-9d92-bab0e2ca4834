import React, { useState, useContext, useEffect, useRef, useCallback } from 'react';
import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import { styled } from '@mui/material/styles';
import Paper from '@mui/material/Paper';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';
import RefreshIcon from '@mui/icons-material/Refresh';

import Typography from '@mui/material/Typography';
import LinearProgress from '@mui/material/LinearProgress';

import { connect } from "react-redux";
import { GetCommonspDataV2 } from "store/actions/CommonAction";

import travellingStatusIcon from '../../../assets/icons/travellingStatus.svg';
import meetingStatusIcon from '../../../assets/icons/meetingStatus.svg';
import callingStatusIcon from '../../../assets/icons/callingStatus.svg';
import logoutStatusIcon from '../../../assets/icons/loggedoutStatus.svg';
import lunchStatusIcon from '../../../assets/icons/lunchStatus.svg';
import idleIcon from '../../../assets/icons/idle-icon.svg';
import appleIcon from '../../../assets/icons/appleIcon.svg';
import androidIcon from '../../../assets/icons/androidIcon.svg';
import waitingAtCX from '../../../assets/icons/waitingAtCX.svg';

// import '../../../assets/scss/_tableview.scss';
import '../../../assets/scss/_mapview.scss';
import moment from 'moment';
import { FosContext } from '../FosContext';
import { ConvertToMinutes, sortByKey } from 'utility/utility';

import { FetchAgentLatLong } from 'store/actions/CommonAction';
import { FetchAgentLatLongByTime } from 'store/actions/CommonAction';
import FosAppointmentJourney from './FosAppointmentJourney';
import _ from 'underscore';
import GoogleMapView_V2 from './GoogleMapView_V2';
import { getUserDetails, gaEventTracker } from 'utility/utility';

import { GoogleMap } from '@react-google-maps/api';
import SlotDisplay from './SlotDisplay';
import OTSHierarchy from './OTSHierarchy';
import { Button } from '@mui/material';

const MapView = (props) => {

    const { fosData, selectedCounter, fullView, triggerComingFrom, selectCounter, untrackabilityTime, otsAvgVal, GetOTSData, checked } = useContext(FosContext);

    const Item = (styled(Paper)(() => ({

    })))
    const [showSingleAgentData, setShowSingleAgentData] = useState(false);

    const [mapData, setMapData] = useState([]);

    const [chosenAgent, setChosenAgent] = useState(null);

    const allAgentData = useRef(new Map());

    const [latLong, _setLatLong] = useState([]);

    const [initialCenterLatLong, _setInitialCenterLatLong] = useState(null);

    const initialCenterLatLongRef = useRef(null);

    const setInitialCenterLatLong = useCallback((val) => {
        initialCenterLatLongRef.current = val;
        _setInitialCenterLatLong(val);

    }, [])

    const [mapDataCopy, setMapDataCopy] = useState([]);

    const apiInterval = useRef(null);

    const mapDataInterval = useRef(null);

    const [agentData, setAgentData] = useState(null);

    const [nonServingLocations, setNonServingLocations] = useState([]);

    const [currentAppointment, setCurrentAppointment] = useState(null);

    const [appointmentHistory, setAppointmentHistory] = useState([]);

    const [currentlocation, setCurrentlocation] = useState(null);

    const [lastUpdatedAt, setLastUpdatedAt] = useState(null);

    const [timedFosData, setTimedFosData] = useState(false);

    const [loader, setLoader] = useState(false);

    const [centerBaseOn, setCenterBaseOn] = useState(null);

    const [baseLocation, setBaseLocation] = useState(null);

    const [chosenSlot, setChosenSlot] = useState(null);

    const [chosenSlotData, setChosenSlotData] = useState([]);

    const [showDifferentSlot, setShowDifferentSlot] = useState(false);

    const [appointmentCard, setAppointmentCard] = useState([]);

    const [journeyCard, setJourneyCard] = useState();

    const [otsModal, setOtsModal] = useState(false);

    const [otsState, setOtsState] = useState(null);

    const [nextVisitExpanded, setNextVisitExpanded] = useState(false);

    const nonCurrentSlot = useRef([]);


    const user = useRef(null);

    const userList = useRef([]);

    function formatDate(date) {
        return moment(date).format('Do MMM, YYYY | hh:mm A');
    }

    const getCurrentSlot = () => {
        let startDate = new Date();
        if (startDate.getHours() % 2 == 1) {
            startDate.setHours(startDate.getHours() - 1);
        }

        startDate.setMinutes(0);
        startDate.setSeconds(0);
        startDate.setMilliseconds(0);

        return { StartDate: startDate, EndDate: new Date() };
    }

    const mapContainerStyle = {
        // width: '910px',
        height: '100vh',
    };

    const GoogleMapOptions = {
        mapTypeControlOptions: {
            mapTypeIds: [] // This removes all map types (including Map and Satellite)
        },
        zoomControl: true, // If you want to keep zoom control
        fullscreenControl: true, // If you want to keep fullscreen control
        styles: [
            {
                featureType: 'poi.business',
                stylers: [{ visibility: 'off' }],
            },

            {
                featureType: 'poi',
                elementType: 'labels',
                stylers: [{ visibility: 'off' }],
            },

            //   {
            //     featureType: 'poi',
            //     elementType: 'labels.text',
            //     stylers: [
            //       {
            //         visibility: 'off',

            //       }
            //     ]
            //   }

        ]
    };


    const setLatLong = (val) => {
        _setLatLong(val);
        if (!showSingleAgentData && Array.isArray(val) && val.length > 0) {
            let avgLat = 0;
            let avgLong = 0;
            for (let i = 0; i < val.length; i++) {

                avgLat = avgLat + val[i].Lat;
                avgLong = avgLong + val[i].Long;
            }

            avgLat = avgLat / val.length;
            avgLong = avgLong / val.length;

            setInitialCenterLatLong({
                lat: avgLat,
                lng: avgLong
            })
        }

    }



    useEffect(() => {
        user.current = parseInt(getUserDetails('UserId'));

        if (!otsAvgVal) return;

        const calculateOtsState = () => {
            if (checked.length > 0) {

                let servingApps = 0;
                let totalApps = 0;

                checked.forEach((node)=>{
                    servingApps += otsAvgVal[node]?.ServingAppointments || 0;
                    totalApps += otsAvgVal[node]?.TotalAppointments || 0;
                })

                return (servingApps/totalApps)*100;
            }

            return otsAvgVal[user.current]?.val || 0;
        };

        setOtsState(calculateOtsState());

    }, [otsAvgVal, checked]);


    useEffect(() => {

        if (mapData &&
            mapData.length > 0) {

            try {

                if (mapDataCopy.length == 0) {

                    mapDataInterval.current = new Date();
                    setMapDataCopy(
                        mapData
                    );

                }
                else {
                    if ((mapDataInterval.current && new Date() - mapDataInterval.current > 1 * 60000) || ['ChangeInCounter', 'Hierarchy'].includes(triggerComingFrom)) {

                        mapDataInterval.current = new Date();
                        setMapDataCopy(mapData);
                    }

                }

                if ((!apiInterval.current || (new Date() - apiInterval.current > 60000 || ['ChangeInCounter', 'Hierarchy'].includes(triggerComingFrom))) && !showSingleAgentData) {

                    setLatLong([])

                    FetchAgentLatLong(userList.current, (errorStatus, data) => {
                        if (!errorStatus && Array.isArray(data) && data.length > 0) {

                            apiInterval.current = new Date();

                            let index2 = _.indexBy(mapData, 'UserId');

                            let mergedArray = _.map(_.filter(data, (obj1) => _.has(index2, obj1.AgentId)), function (obj1) {
                                return _.extend({}, obj1, index2[obj1.AgentId]);
                            });

                            setLatLong(mergedArray);

                        }
                    })
                }
            }
            catch (e) {

            }

        }
    }, [mapData])


    const findSlot = () => {
        let date = new Date();

        switch (date.getHours()) {
            case 8:
            case 9:
                return 1;
            case 10:
            case 11:
                return 2;
            case 12:
            case 13:
                return 3;
            case 14:
            case 15:
                return 4;
            case 16:
            case 17:
                return 5;
            case 18:
            case 19:
                return 6;
            case 20:
            case 21:
                return 7;
        }

        return 0;
    }


    useEffect(() => {

        const FetchLatLongByTime = () => {

            if (currentAppointment) {
                const { StartDate, EndDate } = getCurrentSlot() || {};

                if (StartDate && EndDate) {
                    // Ensure StartDate and EndDate are valid Date objects
                    StartDate.setHours(StartDate.getHours() + 5);
                    StartDate.setMinutes(StartDate.getMinutes() + 30);

                    EndDate.setHours(EndDate.getHours() + 5);
                    EndDate.setMinutes(EndDate.getMinutes() + 30);



                    const Body = {
                        StartTime: StartDate.toISOString(), // Convert to ISO string
                        EndTime: EndDate.toISOString(), // Convert to ISO string
                        AgentId: chosenAgent ? chosenAgent.toString() : "",
                        AppointmentId: parseInt(currentAppointment.AppointmentId, 10)
                    };


                    FetchAgentLatLongByTime(Body, (errorStatus, data1) => {
                        if (errorStatus) {
                            return;
                        }

                        if (data1 && data1.hasOwnProperty('CurrentLocation')) {
                            const agentId = data1['CurrentLocation']['AgentId'];
                            const mapDataItem = mapData.find(item => item['UserId'] === parseInt(agentId, 10));

                            if (mapDataItem) {
                                data1['CurrentLocation']['RealTimeStatusId'] = mapDataItem['RealTimeStatusId'];
                            }

                            let mergeObj = { ...agentData, ...data1['CurrentLocation'] };


                            // data1['CurrentLocation']['WaitingAtCustomer']= agentData['WaitingAtCustomer']? agentData['WaitingAtCustomer'] :false;

                            if (!(initialCenterLatLongRef && initialCenterLatLongRef.current)) {
                                setInitialCenterLatLong({
                                    lat: data1['CurrentLocation'].Lat,
                                    lng: data1['CurrentLocation'].Long
                                })
                            }

                            // setCurrentlocation(data1['CurrentLocation']);

                            setAgentData(mergeObj);
                        }

                        if (data1.hasOwnProperty('LatLongData') && Array.isArray(data1['LatLongData'])) {
                            setAppointmentHistory(data1['LatLongData']);
                        }
                        setLastUpdatedAt(new Date())
                    });
                }
            }

        }

        FetchLatLongByTime();

        const AgentLatLongByTime = setInterval(FetchLatLongByTime, 1 * 60000);

        return () => {

            clearInterval(AgentLatLongByTime)

        };
    }, [currentAppointment]);


    useEffect(() => {

        if (chosenAgent) {

            let data = mapData.find((data => data.UserId === chosenAgent));

            setChosenSlot(findSlot());

            if (data?.AgentBaseLocation) {
                setBaseLocation({
                    lat: data.AgentBaseLocation.Lat,
                    lng: data.AgentBaseLocation.Long
                })
            }
            else {
                setBaseLocation(null)
            }

            setCurrentAppointment(null);
            setNonServingLocations([]);
            setAppointmentCard([]);

            // setCurrentlocation(null);

            setCenterBaseOn(null);

            setInitialCenterLatLong(null);

            props.GetCommonspDataV2({
                root: 'GetAppointmentsByUserId',
                c: "R",
                params: [{ UserId: parseInt(chosenAgent), LeadId: data?.ParentId || 0 }],
            }, (data) => {

                // setCurrentlocation(latLong.find(item => parseInt(item.AgentId) === chosenAgent));
                if (Array.isArray(data?.data?.data) && data.data.data.length > 0) {
                    let appointmentData = data.data.data[0];

                    let currentSlot = findSlot();

                    let differentSlots = [];

                    let tempArray = [];
                    let slotBasedAppointment = _.filter(appointmentData, appointment => {

                        if (appointment.IsServingApp) {

                            let data = null;
                            let pushData = true;
                            for (let i = 0; i < tempArray.length; i++) {
                                if (tempArray[i].LeadId == appointment.LeadId) {
                                    if (new Date(tempArray[i].ActionDoneAt) > new Date(appointment.ActionDoneAt)) {
                                        pushData = false;
                                    }
                                    else {
                                        tempArray = []
                                    }
                                }
                            }


                            if (pushData) {
                                tempArray.push({
                                    LeadId: appointment.LeadId,
                                    CustomerName: appointment.CustomerName,
                                    Status: appointment.AppStatus,
                                    DateTime: null,
                                    Current: true,
                                    ActionDoneAt: appointment.ActionDoneAt
                                });
                                setCurrentAppointment(appointment);
                            }
                        }

                        if (appointment.SlotId != currentSlot) {
                            differentSlots.push(appointment);
                        }


                        if (!appointment.IsServingApp && appointment.SlotId == currentSlot) {
                            let pushData = tempArray.some(data => data.LeadId == appointment.LeadId);

                            if (!pushData) {
                                tempArray.push({
                                    LeadId: appointment.LeadId,
                                    CustomerName: appointment.CustomerName,
                                    Status: appointment.AppStatus,
                                    DateTime: formatDate(new Date(new Date(appointment.AppointmentDateTime).getTime() - (5.5 * 60 * 60 * 1000)))
                                });
                            }
                            return appointment;
                        }
                    });

                    nonCurrentSlot.current = differentSlots;

                    setShowSingleAgentData(true);

                    setNonServingLocations(slotBasedAppointment);

                    let currentAppIndex = 0;
                    for (let i = 0; i < tempArray.length; i++) {
                        if (tempArray[i].Current) {
                            currentAppIndex = i;
                            break;
                        }
                    }

                    if (currentAppIndex != 0) {
                        let tempData = tempArray[0];
                        tempArray[0] = tempArray[currentAppIndex];
                        tempArray[currentAppIndex] = tempData;
                    }
                    setAppointmentCard(tempArray);

                }

                setLoader(false)

            });

        }

    }, [chosenAgent])


    const handleClick = (index, data) => {

        // setAgentData(null);

        setLoader(true);
        let selectAgentLatLong = latLong.find(item => parseInt(item.AgentId) === data.UserId);

        let mergeObj = { ...data, ...selectAgentLatLong };

        setAgentData(mergeObj);

        setChosenAgent(data.UserId);

        let details = {
            UserId: user.current,
            AgentId: data.UserId,
            Time: new Date()
        }

        gaEventTracker('FOSRealTimeTracker',"AgentMapView", details);


    }


    useEffect(() => {

        const timedFosFunc = setTimeout(() => {

            setTimedFosData(!timedFosData)

        }, 2000)

        return () => clearTimeout(timedFosFunc);

    }, [fosData])

    useEffect(() => {

        if (fosData.size > 0) {

            userList.current = [];
            if (['ChangeInCounter', 'Hierarchy'].includes(triggerComingFrom)) {
                setChosenAgent(null);
                setShowSingleAgentData(false);
                setInitialCenterLatLong(null);
                setLastUpdatedAt(null)

            }

            let tempArr = [];

            fosData.forEach((val) => {

                if (!val) {
                    return null
                }

                if (val.UserId) {
                    allAgentData.current.set(val.UserId, val);
                }
                if (selectedCounter != 0 && val['RealTimeStatusId'] != selectedCounter) {
                    return null;
                }

                if (val['RealTimeStatusId'] == '1') {
                    val['status'] = <>Travelling{val['Since'] && ' (' + val['Since']['time'] + ' ' + val['Since']['den'] + ')'}<img src={travellingStatusIcon} alt='Travelling' /></>

                }
                if (val['RealTimeStatusId'] == '2') {
                    val['status'] = <>Meeting {val['Since'] && ' (' + val['Since']['time'] + ' ' + val['Since']['den'] + ')'}<img src={meetingStatusIcon} alt='Meeting' /></>

                }
                if (val['RealTimeStatusId'] == '3') {

                    val['status'] = <>Calling{val['Since'] && ' (' + val['Since']['time'] + ' ' + val['Since']['den'] + ')'}<img src={callingStatusIcon} alt='Calling' /></>

                }
                if (val['RealTimeStatusId'] == '4') {
                    val['status'] = <>Idle{val['Since'] && ' (' + val['Since']['time'] + ' ' + val['Since']['den'] + ')'}<img src={idleIcon} alt='Idle' /></>

                }
                if (val['RealTimeStatusId'] == '5') {
                    if(val['LogoutType'] == 4){
                        val['status'] = <>Lunch<img src={lunchStatusIcon} alt='Logged Off' /></>

                    }else{
                    val['status'] = <>Logged Off{val['Since'] && ' (' + val['Since']['time'] + ' ' + val['Since']['den'] + ')'}<img src={logoutStatusIcon} alt='Logged Off' /></>
                    }
                    val['ParentId'] = 0;

                }
                if (val['RealTimeStatusId'] == '6') {
                    val['status'] = <>Waiting at CX{val['Since'] && ' (' + val['Since']['time'] + ' ' + val['Since']['den'] + ')'}<img src={waitingAtCX} alt='Waiting at CX' /></>
                }

                if (new Date(val['UpdatedOn']).getUTCDate() != new Date().getUTCDate()) {

                    val['TTAttempts'] = null;
                    val['TTTalkTime'] = 0;
                    val['LastAttempt'] = null;
                    val['OTPVerified'] = null;
                    val['OTS'] = null;
                }


                if (chosenAgent && agentData) {

                    if (chosenAgent == val['UserId']) {

                        // selectCounter(parseInt(val['RealTimeStatusId']),1)


                        setAgentData({ ...agentData, ...val });


                    }
                }

                tempArr.push(val);
                userList.current.push(val['UserId'].toString());

            })

            sortByKey(tempArr, 'UserName')
            if(selectedCounter == 4){
                const timeSortedArray = tempArr.sort((a, b) => {
                    const timeA = a?.Since?.time ?? Infinity;
                    const timeB = b?.Since?.time ?? Infinity;
                    return  timeB - timeA;
                  });
                  setMapData(timeSortedArray);
            }else{
                setMapData(tempArr);
            }

         

        }

    }, [timedFosData, selectedCounter])


    useEffect(() => {
        setChosenSlot(findSlot());
    }, [])


    useEffect(() => {

        if (chosenSlot) {
            if (findSlot() == chosenSlot) {
                setShowDifferentSlot(false);
            }
            else {

                let selectedSlotData = [];
                if (nonCurrentSlot?.current.length > 0) {
                    nonCurrentSlot.current.map((appointment) => {
                        if (appointment.SlotId == chosenSlot) {
                            selectedSlotData.push(appointment);
                        }
                    })
                }
                setChosenSlotData(selectedSlotData);
                setShowDifferentSlot(true);
            }
        }

    }, [chosenSlot])


    const changeCenterBaseOn = (status) => {
        setCenterBaseOn(status);
    }

    const findClass = (status) => {

        if (Array.isArray(status.props.children) && status.props.children[0]) {
            if (["Meeting"].includes(status.props.children[0])) {

                return 'status meeting'
            }
            else if (["Idle"].includes(status.props.children[0])) {
                return 'status bound'
            }
            else if (["Logged Off"].includes(status.props.children[0])) {
                
                return 'status loggedOff'
            }
            else if (["Lunch"].includes(status.props.children[0])) {
                
                return 'status lunch'
            }
            else {
                return 'status'
            }

        }
        else {
            return 'status'
        }

    }


    const handleChange = (e) => {

        setChosenSlot(e.target.value);
    };

    const openOTSModal = () => {
        setOtsModal(true);
    }

    const closeOTSModal = () => {
        setOtsModal(false);
    }

    const refreshOtsScore = () => {
        GetOTSData()
    }

    const toggleNextVisitDetails = (event) => {
        event.stopPropagation(); // Prevent parent Grid onClick from firing
        setNextVisitExpanded(!nextVisitExpanded);
    }

    return (

        <div className='map-view'>
            <Grid container spacing={2}>
                <Grid item xs={6} >
                    {
                        otsState != null &&
                        <>
                            <div class="ots-score-card">
                                <div class="ots-header">
                                    <span>Your Team’s OTS Score</span>
                                    <span>{!isNaN(parseInt(otsState)) && otsState !== null ? parseInt(otsState)+'%' : '-'}</span>
                                </div>
                                <progress class="ots-progress" value={otsState} max="100"></progress>
                                {
                                    Object.keys(otsAvgVal).length > 0 &&
                                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginTop: '8px' }}>
                                        <Button
                                            onClick={refreshOtsScore}
                                            size="small"
                                            sx={{
                                                color: '#0065FF',
                                                border: '1px solid #0065FF',
                                                padding: '2px 6px',
                                                fontSize: '0.8rem'
                                            }}
                                            startIcon={<RefreshIcon sx={{ color: '#0065FF', fontSize: '1rem' }} />}
                                            variant="outlined"
                                        >
                                            Refresh
                                        </Button>
                                        <a onClick={openOTSModal} className="ots-link">
                                            Detailed view
                                        </a>
                                    </div>}
                                {/* {Object.keys(otsAvgVal).length>0 && <a onClick={openOTSModal} class="ots-link">Detailed view</a>} */}
                            </div>
                            <Dialog
                                onClose={closeOTSModal}
                                aria-labelledby="customized-dialog-title"
                                open={otsModal}
                                className="OTSHierachyPopup"
                            >
                                <DialogTitle

                                    id="customized-dialog-title"
                                >
                                    <Typography variant="h6">Detailed OTS View</Typography>
                                    <IconButton onClick={closeOTSModal} sx={{ color: (theme) => theme.palette.grey[500] }}>
                                        <CloseIcon />
                                    </IconButton>
                                </DialogTitle>

                                <DialogContent dividers>
                                    <Box className="Score">
                                        {/* Title and Percentage in one row */}
                                        <Typography variant="h6">
                                            Your team's OTS Score
                                            <span>{!isNaN(parseInt(otsState)) && otsState !== null ? parseInt(otsState)+'%' : '-'}</span>
                                      </Typography>


                                        <LinearProgress
                                            variant="determinate"
                                            value={Number(otsState)? Number(otsState):0}
                                            className="ProgressBar"
                                        />
                                    </Box>


                                    <Typography className="Team" variant="h5">Your Team</Typography>
                                    <OTSHierarchy />
                                </DialogContent>
                            </Dialog>
                        </>
                    }
                    {lastUpdatedAt && <div className='time-box'>{moment(lastUpdatedAt).format('HH:mm:ss')}</div>}

                    <div className='left-side' >
                        <Box className={otsState != null ? 'scroll-listing HeightChange' : 'scroll-listing'}>
                            <Grid container spacing={2}>
                                {
                                    selectedCounter == 6 &&
                                    <div className="AdvisorMsg"> <img src="/fosTlDashboard/move_selection_right.svg" /> The status "Advisor at Customer Place" appears if the journey ends
                                        within 2 km of the customer and within 15 minutes.</div>
                                }

                                {

                                    mapData.length > 0 && mapData.map((data, index) => {
                                        return (

                                            <Grid item xs={12} key={data.UserId} onClick={() => handleClick(index, data)}>
                                                <Item
                                                    className={chosenAgent == data.UserId ? 'active' : ''}
                                                >
                                                    <div className='left-content-container'>
                                                    <div className='left-content'>
                                                        {/* <div className="WeekOff"> On Week OFF</div> */}
                                                        {data.isPresent == false && <div className="WeekOff"> On Week OFF</div>}
                                                        <h5 className='userName'>{data.OS && (data.OS == '1' ? <img src={androidIcon} /> : data.OS == '2' ? <img src={appleIcon} /> : null)} {data.UserName ? (data.UserName.length > 30 ? data.UserName.substring(0, 28) + "... | " : data.UserName + ' | ') : "- | "} <span> {data.EmployeeId ? data.EmployeeId : '-'} </span></h5>

                                                        <span>
                                                            {data.ParentId != null && data.ParentId != 0 && (
                                                                <>
                                                                    {data.CustomerName ? (
                                                                        data.CustomerName.length > 27 ? (
                                                                            `${data.CustomerName.substring(0, 24)}...`
                                                                        ) : (
                                                                            data.CustomerName
                                                                        )
                                                                    ) : (
                                                                        "-"
                                                                    )}
                                                                    {' ('}
                                                                    <strong style={{ color: '#0065FF' }}>{data.ParentId}</strong>
                                                                    {')'}
                                                                </>
                                                            )}
                                                        </span>


                                                    </div>
                                                    
                                                    <div>

                                                        {
                                                            data.MethodIdentifier ?
                                                                <span style={{ color: "red" }}>
                                                                    {/* Advisor Location Untrackable */}
                                                                    {data.MethodIdentifier}
                                                                </span>
                                                                :
                                                                ([1, 6].includes(parseInt(data.RealTimeStatusId)) && data.UpdatedOn && untrackabilityTime &&
                                                                    new Date() - new Date(data.UpdatedOn) > untrackabilityTime * 60 * 1000
                                                                    ?
                                                                    <span style={{ color: "red" }}>Advisor Location Untraceable</span>
                                                                    : null
                                                                )
                                                        }
                                                        <span className={findClass(data.status)}>
                                                            {data.status ? data.status : null}
                                                        </span>
                                                    </div>
                                                    <div className={chosenAgent == data.UserId ? 'innerContent show' : 'innerContent'}>
                                                        <div className='inner-list' >
                                                            <ul>

                                                                <li>
                                                                    <label>Verified visits</label>
                                                                    <span>{data.OTPVerified !== null ? data.OTPVerified + " Visits" : "-"}</span>
                                                                </li>
                                                                <li>
                                                                    <label>Total attempts</label>
                                                                    <span>{(data.hasOwnProperty('TTAttempts') && data.TTAttempts != null) ? String(data.TTAttempts) : "-"}</span>
                                                                </li>
                                                                <li>
                                                                    <label>Total talktime</label>
                                                                    <span>{ConvertToMinutes(data.TTTalkTime)}</span>
                                                                </li>
                                                                {/* <li>
                                                                    <label>Last attempt</label>
                                                                    <span>{data.LastAttempt ? (new Date().getUTCFullYear() == new Date(data.LastAttempt).getUTCFullYear() && new Date().getUTCDate() == new Date(data.LastAttempt).getUTCDate() ? moment(data.LastAttempt).format('hh:mm:ss A') : "-") : "-"}</span>
                                                                </li> */}
                                                                <li>
                                                                    <label>OTS</label>
                                                                    <span>{data.OTSValue != null && !isNaN(data.OTSValue) ? parseInt(data.OTSValue) + "%" : '-'}</span>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </div>                                                 
                                                      </div>
                                                    
                                                    {/* Next Visit at Risk Component */}
                                                    <div className="next-visit-risk">
                                                        <div className="risk-header">
                                                            <span className="risk-icon">⚠️</span>
                                                            <span className="risk-title">Next Visit at Risk</span>
                                                            <button 
                                                                className="details-btn" 
                                                                onClick={toggleNextVisitDetails}
                                                            >
                                                                Details {nextVisitExpanded ? '⌃' : '⌄'}
                                                            </button>
                                                        </div>
                                                        
                                                        {nextVisitExpanded && (
                                                            <>
                                                                <div className="visits-section">
                                                                    <div className="visit-date">
                                                                        <span>Visits on 22</span>
                                                                        <sup>nd</sup>
                                                                        <span> June, 2025</span>
                                                                    </div>
                                                                    
                                                                    <div className="visit-entries">
                                                                        <div className="visit-entry">
                                                                            <div className="visit-left">
                                                                                <div className="lead-id">
                                                                                    <span className="label">Lead ID</span>
                                                                                    <span className="value">1365457</span>
                                                                                </div>
                                                                                <div className="customer-name">
                                                                                    <span className="label">Customer Name</span>
                                                                                    <span className="value">Nitish Kumar Tiwari</span>
                                                                                </div>
                                                                            </div>
                                                                            <div className="visit-time">
                                                                                <span className="label">Time</span>
                                                                                <span className="value">5:30 PM</span>
                                                                            </div>
                                                                        </div>
                                                                        
                                                                        <div className="visit-entry">
                                                                            <div className="visit-left">
                                                                                <div className="lead-id">
                                                                                    <span className="label">Lead ID</span>
                                                                                    <span className="value">1365457</span>
                                                                                </div>
                                                                                <div className="customer-name">
                                                                                    <span className="label">Customer Name</span>
                                                                                    <span className="value">Muskaan Jhorad</span>
                                                                                </div>
                                                                            </div>
                                                                            <div className="visit-time">
                                                                                <span className="label">Time</span>
                                                                                <span className="value">6:30 PM</span>
                                                                            </div>
                                                                        </div>
                                                                        
                                                                        <div className="visit-entry">
                                                                            <div className="visit-left">
                                                                                <div className="lead-id">
                                                                                    <span className="label">Lead ID</span>
                                                                                    <span className="value">1365457</span>
                                                                                </div>
                                                                                <div className="customer-name">
                                                                                    <span className="label">Customer Name</span>
                                                                                    <span className="value">Vineet Yadav</span>
                                                                                </div>
                                                                            </div>
                                                                            <div className="visit-time">
                                                                                <span className="label">Time</span>
                                                                                <span className="value">7:30 PM</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                
                                                                <div className="visits-section">
                                                                    <div className="visit-date">
                                                                        <span>Visits on 23</span>
                                                                        <sup>rd</sup>
                                                                        <span> June, 2025</span>
                                                                    </div>
                                                                    
                                                                    <div className="visit-entries">
                                                                        <div className="visit-entry">
                                                                            <div className="visit-left">
                                                                                <div className="lead-id">
                                                                                    <span className="label">Lead ID</span>
                                                                                    <span className="value">1365457</span>
                                                                                </div>
                                                                                <div className="customer-name">
                                                                                    <span className="label">Customer Name</span>
                                                                                    <span className="value">Nitish Kumar Tiwari</span>
                                                                                </div>
                                                                            </div>
                                                                            <div className="visit-time">
                                                                                <span className="label">Time</span>
                                                                                <span className="value">11:30 AM</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </>
                                                        )}
                                                    </div>
                                                </Item>
                                            </Grid>

                                        )
                                    })

                                }
                            </Grid>
                        </Box>
                    </div>


                </Grid>

                <Grid item xs={6} >


                    <div className={fullView == false ? 'ryt-side' : 'ryt-side fullDetailview'}>
                        <div className={fullView == false ? 'mapscreen' : 'fullview'}>

                            {
                                latLong &&
                                <div className="dvmapview">
                                    {/* {
                                        chosenAgent &&
                                        <SlotDisplay
                                            handleChange={handleChange}
                                            chosenSlot={chosenSlot}
                                        />
                                    } */}
                                    <GoogleMap
                                        mapContainerStyle={mapContainerStyle}
                                        zoom={10}
                                        center={initialCenterLatLong}
                                        options={GoogleMapOptions}

                                    >
                                        <GoogleMapView_V2
                                            showSingleAgentData={showSingleAgentData}
                                            latLong={latLong}
                                            currentAppointment={currentAppointment}
                                            nonServingLocations={nonServingLocations}
                                            currentLocation={agentData}
                                            appointmentHistory={appointmentHistory}
                                            chosenAgent={chosenAgent}
                                            centerBaseOn={centerBaseOn}
                                            baseLocation={baseLocation}
                                            setInitialCenterLatLong={setInitialCenterLatLong}

                                            showDifferentSlot={showDifferentSlot}
                                            nonCurrentSlot={chosenSlotData}


                                        />
                                    </GoogleMap>

                                </div>
                            }


                        </div>
                        {chosenAgent != null && agentData != null && <div className='journeyBox'> <FosAppointmentJourney data={agentData} appointmentCard={appointmentCard} changeCenterBaseOn={changeCenterBaseOn} /> </div>}



                    </div>

                </Grid>
            </Grid>
        </div>
    );

}

function mapStateToProps(state) {
    return {
        CommonData: state.CommonData
    };
}
export default connect(
    mapStateToProps,
    {

        GetCommonspDataV2,

    }
)(MapView);