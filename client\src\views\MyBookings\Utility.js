import { GetCommonApiData, GetCommonData, GetCommonspDataV2, PostCommonApiData ,ValidateAddLeadToPriorityQueue} from "../../store/actions/CommonAction";
import { set<PERSON><PERSON>ie, LeadContentView, <PERSON>View, getuser } from "../../utility/utility";
import moment from 'moment';
import config from "../../config";
import axios from "axios";
export const ALL = 1000;
export const ALL_AGENTS = 'ALL_AGENTS';
export const ALL_BOOKINGS_TEXT = 'All your bookings for selected month and product';
export const SECONDARY_BOOKINGS_TEXT = 'All your bookings from FOS and other assisted sales channels';
export const MANAGER_ABOVE_MESSAGE_NOTE = '* Select Product, BookingMonth and Supervisors from Show hierarchy button, then select agent from dropdown.';
export const AGENT_MESSAGE_NOTE = '* Select your Product and Booking Month to view your bookings.';
export const SUPERVISOR_MESSAGE_NOTE = '* Select Product and Booking Month, then select agent from dropdown';
export const HIERARCHY_MESSAGE_NOTE = '* Select only one TL from Hierarchy Drop down';
export const SECONDARY = 2000;
export const ISADMIN = false;
export const VERIFICATION_ADDED_LEADS = 'verificationAddedLeads'

export const ProductMap = {
  7: "term",
  117: "motor",
  115: "investments",
  2: "health",
  131: "sme"
}

export const NavBarButtonsSlider = {
  "term": [
    { id: 7, BookingStatusType: 7, text: 'Issued', description: 'All your bookings in Issued Status.' },
    { id: 18, BookingStatusType: 18, text: 'Referral Opportunity', description: 'All your bookings which are Issued within 20 days of Lead booking.' },
    { id: 1, BookingStatusType: 1, text: 'Auto Pay Pending', description: 'Auto Pay Pending - Please connect with customer to enable Auto Pay(SI / Emandate).' },
    { id: 2, BookingStatusType: 2, text: 'PF Pending', description: 'Proposal Form is pending or incomplete for these bookings - connect with customer to complete the PF.' },
    // { id: 3, BookingStatusType: 3, text: 'Docs-Medical-PIVC Pending', description: 'Documents or Medical Tests Pending - Connect with customer and help in closing the required documents/medical tests.' },
    { id: 4, BookingStatusType: 4, text: 'Retention', description: 'Customer had cancelled the booking - Connect with customer to retain the booking.' },
    // { id: 5, BookingStatusType: 5, text: 'Additional Docs and Medical Pending', description: 'Additional Docs or Medical raised by insurer - Connect the customer to close this and avoid booking cancellation.' },
    { id: 6, BookingStatusType: 6, text: 'Counter Offer Pending', description: 'Counter offer presented by insurer - Connect with customer to make them understand and accept the offer.' },
    { id: 13, BookingStatusType: 13, text: 'Free Look Cancellation', description: 'Booking cancelled in free lookup period.' },
    { id: 14, BookingStatusType: 14, text: 'Refund / Rejected', description: 'Refund Completed / Rejected Bookings'  },
    { id: 15, BookingStatusType: 15, text: 'Additional Docs- Medical- PIVC Pending', description: 'Documents or Medical Tests Pending - Connect with customer and help in closing the required documents/medical tests.'},
    // { id: 100, text: '', BookingStatusType: 100 },
  ],

  "investments": [
    { id: 8, BookingStatusType: 8, text: 'Verification-Fatal', css: 'red', activeClass: ' RedActiveBtn', description: 'Bookings with fatal mis-selling cases.' },
    { id: 12, BookingStatusType: 12, text: 'Verification Pending', css:'red', activeClass: ' RedActiveBtn', description: 'Counter offer presented by insurer - Connect with customer to make them understand and accept the offer.'},
    { id: 9, BookingStatusType: 9, text: 'Verification - MisCommitment', css: 'yellow', activeClass: ' yellowActiveBtn', description: 'Mis-sell Bookings connect with customer to clarify / rectify the confusion.' },
    { id: 10, BookingStatusType: 10, text: 'SLA Breached', css: 'orange', activeClass: ' orangeActiveBtn', description: 'No action had been done on mis-commitment leads from past 3-days.' },
    { id: 15, BookingStatusType: 15, text: 'Additional Docs- Medical- PIVC Pending', description: 'Documents or Medical Tests Pending - Connect with customer and help in closing the required documents/medical tests.'},
    { id: 7, BookingStatusType: 7, text: 'Issued', description: 'All your bookings in Issued Status.' },
    { id: 1, BookingStatusType: 1, text: 'Auto Pay Pending', description: 'Auto Pay Pending - Please connect with customer to enable Auto Pay(SI / Emandate).' },
    { id: 2, BookingStatusType: 2, text: 'PF Pending', description: 'Proposal Form is pending or incomplete for these bookings - connect with customer to complete the PF.' },
    // { id: 3, BookingStatusType: 3, text: 'Docs-Medical-PIVC Pending', description: 'Documents or Medical Tests Pending - Connect with customer and help in closing the required documents/medical tests.' },
    { id: 4, BookingStatusType: 4, text: 'Retention', description: 'Customer had cancelled the booking - Connect with customer to retain the booking.' },
    // { id: 5, BookingStatusType: 5, text: 'Additional Docs and Medical Pending', description: 'Additional Docs or Medical raised by insurer - Connect the customer to close this and avoid booking cancellation.' },
    { id: 6, BookingStatusType: 6, text: 'Counter Offer Pending', description: 'Counter offer presented by insurer - Connect with customer to make them understand and accept the offer.' },
    { id: 13, BookingStatusType: 13, text: 'Free Look Cancellation', description: 'Booking cancelled in free lookup period.' },
    { id: 14, BookingStatusType: 14, text: 'Refund / Rejected', description: 'Refund Completed / Rejected Bookings'  },
    
    // { id: 100, text: '', BookingStatusType: 100 },
  ],
  "motor": [
    { id: 1, BookingStatusType: 1, text: 'Issued', description: 'All your bookings in Issued Status.' },
    { id: 2, BookingStatusType: 2, text: 'PSU', description: 'All your PSU booking listed.' },
    { id: 3, BookingStatusType: 3, text: 'Non PSU', description: 'All your Non-PSU booking listed.' },
    { id: 4, BookingStatusType: 4, text: 'Documents Pending', description: 'Documents Pending - Connect with customer and help in closing the required documents.' },
    { id: 5, BookingStatusType: 5, text: 'Documentation and Inspection', description: 'Connect with customer and help in closing the document and inspection parts.' },
    { id: 6, BookingStatusType: 6, text: 'Retention', description: 'Customer had cancelled the booking - Connect with customer to retain the booking.' },
    { id: 7, BookingStatusType: 7, text: 'Refund / Rejected', description: 'Refund Completed / Rejected Bookings' },
    //{ id: 6, BookingStatusType: 6, text: 'Health',description: 'Counter offer presented by insurer - Connect with customer to make them understand and accept the offer.' },
    // { id: 100, text: '', BookingStatusType: 100 },
  ],
  "health": [
    { id: 16, BookingStatusType: 16, text: 'Missell', description: "Missell bookings", css:'red', activeClass: ' RedActiveBtn'},
    { id: 7, BookingStatusType: 7, text: 'Issued', description: 'All your bookings in Issued Status.' },
    //{ id: 1, text: 'Auto Pay Pending', BookingStatusType: 1 },
    { id: 2, BookingStatusType: 2, text: 'PF Pending', description: 'Proposal Form is pending or incomplete for these bookings - connect with customer to complete the PF.' },
    // { id: 3, BookingStatusType: 3, text: 'Docs-Medical-PIVC Pending', description: 'Documents or Medical Tests Pending - Connect with customer and help in closing the required documents/medical tests.' },
    { id: 4, BookingStatusType: 4, text: 'Retention', description: 'Customer had cancelled the booking - Connect with customer to retain the booking.' },
    // { id: 5, BookingStatusType: 5, text: 'Additional Docs and Medical Pending', description: 'Additional Docs or Medical raised by insurer - Connect the customer to close this and avoid booking cancellation.' },
    { id: 6, BookingStatusType: 6, text: 'Counter Offer Pending', description: 'Counter offer presented by insurer - Connect with customer to make them understand and accept the offer.' },
    { id: 11, BookingStatusType: 11, text: 'KYC Pending', description: 'Additional Docs or Medical raised by insurer - Connect the customer to close this and avoid booking cancellation.' },
    { id: 12, BookingStatusType: 12, text: 'Verification Pending', description: 'Counter offer presented by insurer - Connect with customer to make them understand and accept the offer.' },
    { id: 14, BookingStatusType: 14,text: 'Refund / Rejected', description: 'Refund Completed / Rejected Bookings'  },
    { id: 15, BookingStatusType: 15, text: 'Additional Docs- Medical- PIVC Pending', description: 'Documents or Medical Tests Pending - Connect with customer and help in closing the required documents/medical tests.'},
    //{ id: 11, BookingStatusType: 11, text: 'Super TopUp', description: 'All your bookings in Issued Status.'},
    { id: 17, BookingStatusType: 17, text: 'Claim Taken', description: "Has Customer taken any claim previously?"},
    //{ id: 100, text: '', BookingStatusType: 100 },
  ],
  "sme": [
    { id: 7, BookingStatusType: 7, text: 'Issued', description: 'All your bookings in Issued Status.' },
    { id: 1, BookingStatusType: 1, text: 'Auto Pay Pending', description: 'Auto Pay Pending - Please connect with customer to enable Auto Pay(SI / Emandate).' },
    { id: 2, BookingStatusType: 2, text: 'PF Pending', description: 'Proposal Form is pending or incomplete for these bookings - connect with customer to complete the PF.' },
    // { id: 3, BookingStatusType: 3, text: 'Docs-Medical-PIVC Pending', description: 'Documents or Medical Tests Pending - Connect with customer and help in closing the required documents/medical tests.' },
    { id: 4, BookingStatusType: 4, text: 'Retention', description: 'Customer had cancelled the booking - Connect with customer to retain the booking.' },
    // { id: 5, BookingStatusType: 5, text: 'Additional Docs and Medical Pending', description: 'Additional Docs or Medical raised by insurer - Connect the customer to close this and avoid booking cancellation.' },
    { id: 6, BookingStatusType: 6, text: 'Counter Offer Pending', description: 'Counter offer presented by insurer - Connect with customer to make them understand and accept the offer.' },
    { id: 13, BookingStatusType: 13, text: 'Free Look Cancellation', description: 'Booking cancelled in free lookup period.' },
    { id: 14, BookingStatusType: 14, text: 'Refund / Rejected', description: 'Refund Completed / Rejected Bookings'  },
    { id: 15, BookingStatusType: 15, text: 'Additional Docs- Medical- PIVC Pending', description: 'Documents or Medical Tests Pending - Connect with customer and help in closing the required documents/medical tests.'},
    // { id: 100, text: '', BookingStatusType: 100 },
  ],
}

export const TableColumns =
{
  "term": [
    { name: "S. No", accessor: "Id", type: "", dependents: [] },
    { name: "SalesAgent", accessor: "UserName", type: "", dependents: [] },
    { name: "BookingID", accessor: "BookingID", type: "iframe", show: true, dependents: [] },
    { name: "ReferralInfo", accessor: "Referrals", type: "popup", show: false, text: "View Data", dependents: [], conditions: [{key:'activeFilter', value:18}] },
    { name: "BookingDate", accessor: "BookingDate", type: "datetime", dependents: [] },
    { name: "CustomerName", accessor: "CustomerName", type: "", dependents: [] },
    { name: "Category", accessor: "Category", type: "", dependents: [], conditions: [{key:'activeTab', value:'Secondary'}] },
    { name: "Status", accessor: "StatusName", type: "iframe", show: true, dependents: [] },
    { name: "SubStatus", accessor: "SubStatusName", type: "", dependents: [] },
    { name: "StatusDate", accessor: "StatusDate", type: "date", dependents: [] },
    { name: "ContinueJourney", accessor: "ExitPointURL", type: "iframe",  text: 'ContinueJourney', show: false, dependents: [] },
    { name: "MobileNumber", accessor: "MobileNumber", type: "call", dependents: [], conditions: [{key:'activeTab', value:'Primary'}] },
    { name: "PaymentE2E", accessor: "PaymentE2EFlag", type: "boolean", dependents: [] },
    { name: "PFFilled", accessor: "ProposalFillFlag", type: "boolean", dependents: [] },
    { name: "PFFilledDate", accessor: "ProposalFillDate", type: "date", dependents: [] },
    { name: "ApplicationNo", accessor: "ApplicationNo", type: "", dependents: [] },
    { name: "CustomerID", accessor: "CustomerID", type: "", dependents: [] },
    { name: "ParentID", accessor: "ParentID", type: "", dependents: [] },
    { name: "Insurer", accessor: "Insurer", type: "", dependents: [] },
    { name: "PlanName", accessor: "PlanName", type: "", dependents: [] },
    { name: "CountryName", accessor: "CountryName", type: "", dependents: [] },
    { name: "ViewRemarks", accessor: "ViewRemarks", type: "popup", text: 'ViewRemarks', show: false, dependents: [] },
    { name: "AddRemarks", accessor: "AddRemarks", type: "popup", text: 'AddRemarks', show: false, dependents: [] },
    { name: "TotalPremium", accessor: "TotalPremium", type: "int", dependents: [] },
    // { name: "AutoDebitStatus", accessor: "IsSI", type: "popup", text: "Check status", show: true, dependents: [] },
    { name: "AutoDebitStatus", accessor: "StatusType", type: "active", dependents: [] },
    {name: "AutoDebitPrompt", accessor:"StatusType",type:"prompt",text:"",show: true,dependents:[]},
    { name: "DaysFromLastUpdate", accessor: "DaysFromLastUpdate", type: "", dependents: [] },
    { name: "DaysFromPayment", accessor: "DaysFromPayment", type: "", dependents: [] },
    { name: "MisSelling", accessor: "MisSelling", type: "boolean", dependents: [] },
    { name: "MissellingStatus", accessor: "MissellingStatus", type: "", dependents: [] },
    { name: "PIVCStatus", accessor: "PIWCStatus", type: "", dependents: [] },
    { name: "PIVCLink", accessor: "PIWCLink", type: "link", text: 'PIVCLink', show: false, dependents: [] },
    { name: "SumInsured", accessor: "SumInsured", type: "int", dependents: [] },
    { name: "APE", accessor: "APE", type: "int", dependents:[]},
    { name: "MedicalType", accessor: "MedicalType", type: "", dependents: [] },
    { name: "Financial Waiver", accessor: "FinancialWaived", type: "", dependents: [] },
    { name: "IsStp", accessor: "IsSTP", type: "boolean", dependents: [] },
    { name: "KYCType", accessor: "KYCType", type: "", dependents: [] },
    { name: "KYCStatus", accessor: "KYCStatus", type: "", dependents: [] },
    { name: "OneClickRefunded", accessor: "OneClickReason", type: "", dependents: [] },
    { name: "FirstPremiumPaidOnly", accessor: "FirstPremiumPaidOnly", type: "boolean", dependents: [] },
    { name: "FreeLookCancellation", accessor: "FreeLookCancellation", type: "boolean", dependents: [] },
    { name:"Add MoM", accessor:"AddMom", type:"iframe" , subtype:"AddMom", dependents:[], conditions: [{key:'IsSmeFosAgent', value: true}]},
    { name:"View MoM", accessor:"ViewMom", type:"iframe" , subtype:"ViewMom", dependents:[], conditions: [{key:'IsSmeFosAgent', value: true}]},
    { name: "Upload Docs", accessor: "UploadDocs", type: "iframe", subtype: 'upload', dependents: [] },
    // {name:"Opt For WA", accessor:"NewButton", type:"iframe" , subtype:"optForWA", dependents:[]},
  ],

  "investments": [
    { name: "S. No", accessor: "Id", type: "", dependents: [] },
    { name: "SalesAgent", accessor: "UserName", type: "", dependents: [] },
    { name: "BookingID", accessor: "BookingID", type: "iframe", show: true, dependents: [] },
    { name: "BookingDate", accessor: "BookingDate", type: "datetime", dependents: [] },
    { name: "CustomerName", accessor: "CustomerName", type: "", dependents: [] },
    { name: "Status", accessor: "StatusName", type: "iframe", show: true, dependents: [] },
    { name: "SubStatus", accessor: "SubStatusName", type: "", dependents: [] },
    { name: "StatusDate", accessor: "StatusDate", type: "date", dependents: [] },
    { name: "ContinueJourney", accessor: "ExitPointURL", type: "iframe", text: 'ContinueJourney', show: false, dependents: [] },
    { name: "MobileNumber", accessor: "MobileNumber", type: "call", dependents: [], conditions: [{key:'activeTab', value:'Primary'}] },
    { name: "PaymentE2E", accessor: "PaymentE2EFlag", type: "boolean", dependents: [] },
    { name: "PFFilled", accessor: "ProposalFillFlag", type: "boolean", dependents: [] },
    { name: "PFFilledDate", accessor: "ProposalFillDate", type: "date", dependents: [] },
    { name: "VerificationStatus", accessor: "VerificationStatus", type: "", dependents: [] },
    { name: "VerificationSubStatus", accessor: "VerificationSubStatus", type: "", dependents: [] },
    {
      name: "Verification Remark", accessor: "VerificationRemark", type: "popup", show: false, subtype: '', text: 'Take Action', dependents: [
        { key: "VerificationStatus", value: "Verification Failed" },
        { key: "VerificationSubStatus", value: "Mis-Commitment (Yellow Category)" }
      ]
    },
    { name: "Verification Comment", accessor: "Remarks", type: "popup", text: "", show: true, dependents: [], overflow: true },
    { name: "ApplicationNo", accessor: "ApplicationNo", type: "", dependents: [] },
    { name: "CustomerID", accessor: "CustomerID", type: "", dependents: [] },
    { name: "ParentID", accessor: "ParentID", type: "", dependents: [] },
    { name: "Insurer", accessor: "Insurer", type: "", dependents: [] },
    { name: "PlanName", accessor: "PlanName", type: "", dependents: [] },
    { name: "CountryName", accessor: "CountryName", type: "", dependents: [] },
    { name: "ViewRemarks", accessor: "ViewRemarks", type: "popup", text: 'ViewRemarks', show: false, dependents: [] },
    { name: "AddRemarks", accessor: "AddRemarks", type: "popup", text: 'AddRemarks', show: false, dependents: [] },
    { name: "TotalPremium", accessor: "TotalPremium", type: "int", dependents: [] },
    { name: "AutoDebitStatus", accessor: "StatusType", type: "active", dependents: [] },
    {name: "AutoDebitPrompt", accessor:"StatusType",type:"prompt",text:"",show: true,dependents:[]},
    { name: "DaysFromLastUpdate", accessor: "DaysFromLastUpdate", type: "", dependents: [] },
    { name: "DaysFromPayment", accessor: "DaysFromPayment", type: "", dependents: [] },
    { name: "MisSelling", accessor: "MisSelling", type: "boolean", dependents: [] },
    { name: "MissellingStatus", accessor: "MissellingStatus", type: "", dependents: [] },
    { name: "PIVCStatus", accessor: "PIWCStatus", type: "", dependents: [] },
    { name: "PIVCLink", accessor: "PIWCLink", type: "link", text: 'PIVCLink', show: false, dependents: [] },
    { name: "SumInsured", accessor: "SumInsured", type: "int", dependents: [] },
    { name: "APE", accessor: "APE", type: "int", dependents:[]},
    { name: "MedicalType", accessor: "MedicalType", type: "", dependents: [] },
    { name: "Financial Waiver", accessor: "FinancialWaived", type: "", dependents: [] },
    { name: "IsStp", accessor: "IsSTP", type: "boolean", dependents: [] },
    { name: "KYCType", accessor: "KYCType", type: "", dependents: [] },
    { name: "KYCStatus", accessor: "KYCStatus", type: "", dependents: [] },
    { name: "OneClickRefunded", accessor: "OneClickReason", type: "", dependents: [] },
    { name: "FirstPremiumPaidOnly", accessor: "FirstPremiumPaidOnly", type: "boolean", dependents: [] },
    { name: "FreeLookCancellation", accessor: "FreeLookCancellation", type: "boolean", dependents: [] },
    { name: "Upload Docs", accessor: "UploadDocs", type: "iframe", subtype: 'upload', dependents: [] },
  
  ],
  "motor": [
    { name: "S. No", accessor: "Id", type: "", dependents: [] },
    { name: "SalesAgent", accessor: "UserName", type: "", dependents: [] },
    { name: "BookingID", accessor: "BookingID", type: "iframe", show: true, dependents: [] },
    { name: "BookingDate", accessor: "BookingDate", type: "datetime", dependents: [] },
    { name: "CustomerName", accessor: "CustomerName", type: "", dependents: [] },
    { name: "Status", accessor: "StatusName", type: "iframe", show: true, dependents: [] },
    { name: "SubStatus", accessor: "SubStatusName", type: "", dependents: [] },
    { name: "StatusDate", accessor: "StatusDate", type: "date", dependents: [] },
    { name: "ContinueJourney", accessor: "ExitPointURL", type: "iframe", text: 'ContinueJourney', show: false, dependents: [] },
    { name: "MobileNumber", accessor: "MobileNumber", type: "call", dependents: [], conditions: [{key:'activeTab', value:'Primary'}] },
    { name: "KYCType", accessor: "KYCType", type: "", dependents: [] },
    { name: "KYCStatus", accessor: "KYCStatus", type: "", dependents: [] },
    { name: "PaymentE2E", accessor: "PaymentE2EFlag", type: "boolean", dependents: [] },
    { name: "PFFilled", accessor: "ProposalFillFlag", type: "boolean", dependents: [] },
    { name: "ApplicationNo", accessor: "ApplicationNo", type: "", dependents: [] },
    { name: "MisSelling", accessor: "MisSelling", type: "boolean", dependents: [] },
    { name: "MissellingStatus", accessor: "MissellingStatus", type: "", dependents: [] },
    { name: "DaysFromLastUpdate", accessor: "DaysFromLastUpdate", type: "", dependents: [] },
    { name: "DaysFromPayment", accessor: "DaysFromPayment", type: "", dependents: [] },
    { name: "CustomerID", accessor: "CustomerID", type: "", dependents: [] },
    { name: "ViewRemarks", accessor: "ViewRemarks", type: "popup", text: 'ViewRemarks', show: false, dependents: [] },
    { name: "Insurer", accessor: "Insurer", type: "", dependents: [] },
    { name: "PlanName", accessor: "PlanName", type: "", dependents: [] },
    { name: "CountryName", accessor: "CountryName", type: "", dependents: [] },
    { name: "TotalPremium", accessor: "TotalPremium", type: "int", dependents: [] },
    { name: "IDV", accessor: "SumInsured", type: "int", dependents: [] },
    { name: "APE",accessor: "APE", type: "int", dependents: []},
    // { name: "MakeModelVariant", accessor: "MakeModelVariant", type: "", dependents: [] },
    { name: "PolicyExpiryDate", accessor: "PolicyExpiryDate", type: "date", dependents: [] },
    { name: "ParentID", accessor: "ParentID", type: "", dependents: [] },
    { name: "AddRemarks", accessor: "AddRemarks", type: "popup", text: 'AddRemarks', show: false, dependents: [] },
    { name: "IsStp", accessor: "IsSTP", type: "boolean", dependents: [] },
    { name: "Upload Docs", accessor: "UploadDocs", type: "iframe", subtype: 'upload', dependents: [] },
    
  ],
  "health": [
    { name: "S. No", accessor: "Id", type: "", dependents: [] },
    { name: "SalesAgent", accessor: "UserName", type: "", dependents: [] },
    { name: "BookingID", accessor: "BookingID", type: "iframe", show: true, dependents: [] },
    { name: "BookingDate", accessor: "BookingDate", type: "datetime", dependents: [] },
    { name: "CustomerName", accessor: "CustomerName", type: "", dependents: [] },
    { name: "Status", accessor: "StatusName", type: "iframe", show: true, dependents: [] },
    { name: "SubStatus", accessor: "SubStatusName", type: "", dependents: [] },
    { name: "StatusDate", accessor: "StatusDate", type: "date", dependents: [] },
    { name: "Claim Taken", accessor: "ClaimTaken", type: "boolean", dependents: [] },
    { name: "Claim Amount", accessor: "ClaimAmount", type: "rupees", dependents: []},
    { name: "ContinueJourney", accessor: "ExitPointURL", type: "iframe", text: 'ContinueJourney', show: false, dependents: [] },
    { name: "MobileNumber", accessor: "MobileNumber", type: "call", dependents: [], conditions: [{key:'activeTab', value:'Primary'}] },
    { name: "KYCType", accessor: "KYCType", type: "", dependents: [] },
    { name: "KYCStatus", accessor: "KYCStatus", type: "", dependents: [] },
    { name: "PaymentE2E", accessor: "PaymentE2EFlag", type: "boolean", dependents: [] },
    { name: "PFFilled", accessor: "ProposalFillFlag", type: "boolean", dependents: [] },
    { name: "ApplicationNo", accessor: "ApplicationNo", type: "", dependents: [] },
    {
      name: "VerificationStatus", accessor: "VerificationStatus", type: "iframe", show: true, disable: true,
       subtype: '', text: '', dependents: [
        { key: "VerificationStatus", value: "Pending" },
      ]
    },
    {
      name: "AddToVerificationCallQueue", accessor: "VerificationStatus", type: "link", show: false, disable: true,
        subtype: '', text: 'Add call to queue', successText: 'Added', dependents: [
        { key: "VerificationStatus", value: "Pending" },
      ]
    },
    { name: "MisSelling", accessor: "MisSelling", type: "boolean", dependents: [] },
    { name: "MissellingStatus", accessor: "MissellingStatus", type: "", dependents: [] },
    { name: "DaysFromLastUpdate", accessor: "DaysFromLastUpdate", type: "", dependents: [] },
    { name: "DaysFromPayment", accessor: "DaysFromPayment", type: "", dependents: [] },
    { name: "CustomerID", accessor: "CustomerID", type: "", dependents: [] },
    { name: "ViewRemarks", accessor: "ViewRemarks", type: "popup", text: 'ViewRemarks', show: false, dependents: [] },
    { name: "Insurer", accessor: "Insurer", type: "", dependents: [] },
    { name: "PlanName", accessor: "PlanName", type: "", dependents: [] },
    { name: "CountryName", accessor: "CountryName", type: "", dependents: [] },
    { name: "TotalPremium", accessor: "TotalPremium", type: "int", dependents: [] },
    { name: "AutoDebitStatus", accessor: "StatusType", type: "active", dependents: [] },
    {name: "AutoDebitPrompt", accessor:"StatusType",type:"prompt",text:"",show: true,dependents:[]},
    { name: "PolicyExpiryDate", accessor: "PolicyExpiryDate", type: "date", dependents: [] },
    { name: "ParentID", accessor: "ParentID", type: "", dependents: [] },
    { name: "AddRemarks", accessor: "AddRemarks", type: "popup", text: 'AddRemarks', show: false, dependents: [] },
    { name: "IsStp", accessor: "IsSTP", type: "boolean", dependents: [] },
    { name: "SumInsured", accessor: "SumInsured", type: "int", dependents: [] },
    { name: "APE", accessor: "APE", type: "int", dependents:[]},
    { name: "Upload Docs", accessor: "UploadDocs", type: "iframe", subtype: 'upload', dependents: [] },
   
  ],
  "sme": [
    { name: "S. No", accessor: "Id", type: "", dependents: [] },
    { name: "SalesAgent", accessor: "UserName", type: "", dependents: [] },
    { name: "BookingID", accessor: "BookingID", type: "iframe", show: true, dependents: [] },
    { name: "BookingDate", accessor: "BookingDate", type: "datetime", dependents: [] },
    { name: "CustomerName", accessor: "CustomerName", type: "", dependents: [] },
    { name: "Status", accessor: "StatusName", type: "iframe", show: true, dependents: [] },
    { name: "SubStatus", accessor: "SubStatusName", type: "", dependents: [] },
    { name: "StatusDate", accessor: "StatusDate", type: "date", dependents: [] },
    { name: "ContinueJourney", accessor: "ExitPointURL", type: "iframe",  text: 'ContinueJourney', show: false, dependents: [] },
    { name: "MobileNumber", accessor: "MobileNumber", type: "call", dependents: [], conditions: [{key:'activeTab', value:'Primary'}] },
    { name: "PaymentE2E", accessor: "PaymentE2EFlag", type: "boolean", dependents: [] },
    { name: "PFFilled", accessor: "ProposalFillFlag", type: "boolean", dependents: [] },
    { name: "PFFilledDate", accessor: "ProposalFillDate", type: "date", dependents: [] },
    { name: "ApplicationNo", accessor: "ApplicationNo", type: "", dependents: [] },
    { name: "CustomerID", accessor: "CustomerID", type: "", dependents: [] },
    { name: "ParentID", accessor: "ParentID", type: "", dependents: [] },
    { name: "Insurer", accessor: "Insurer", type: "", dependents: [] },
    { name: "PlanName", accessor: "PlanName", type: "", dependents: [] },
    { name: "CountryName", accessor: "CountryName", type: "", dependents: [] },
    { name: "ViewRemarks", accessor: "ViewRemarks", type: "popup", text: 'ViewRemarks', show: false, dependents: [] },
    { name: "AddRemarks", accessor: "AddRemarks", type: "popup", text: 'AddRemarks', show: false, dependents: [] },
    { name: "TotalPremium", accessor: "TotalPremium", type: "int", dependents: [] },
    { name: "AutoDebitStatus", accessor: "StatusType", type: "active", dependents: [] },
    {name: "AutoDebitPrompt", accessor:"StatusType",type:"popup",text:"Do you want to proceed?",show: true,dependents:[]},
    // { name: "AutoDebitStatus", accessor: "IsSI", type: "popup", text: "Check status", show: true, dependents: [] },
    { name: "DaysFromLastUpdate", accessor: "DaysFromLastUpdate", type: "", dependents: [] },
    { name: "DaysFromPayment", accessor: "DaysFromPayment", type: "", dependents: [] },
    { name: "MisSelling", accessor: "MisSelling", type: "boolean", dependents: [] },
    { name: "MissellingStatus", accessor: "MissellingStatus", type: "", dependents: [] },
    { name: "PIVCStatus", accessor: "PIWCStatus", type: "", dependents: [] },
    { name: "PIVCLink", accessor: "PIWCLink", type: "link", text: 'PIVCLink', show: false, dependents: [] },
    { name: "SumInsured", accessor: "SumInsured", type: "int", dependents: [] },
    { name: "APE", accessor: "APE", type: "int", dependents:[]},
    { name: "MedicalType", accessor: "MedicalType", type: "", dependents: [] },
    { name: "Financial Waiver", accessor: "FinancialWaived", type: "", dependents: [] },
    { name: "IsStp", accessor: "IsSTP", type: "boolean", dependents: [] },
    { name: "KYCType", accessor: "KYCType", type: "", dependents: [] },
    { name: "KYCStatus", accessor: "KYCStatus", type: "", dependents: [] },
    { name: "OneClickRefunded", accessor: "OneClickReason", type: "", dependents: [] },
    { name: "FirstPremiumPaidOnly", accessor: "FirstPremiumPaidOnly", type: "boolean", dependents: [] },
    { name: "FreeLookCancellation", accessor: "FreeLookCancellation", type: "boolean", dependents: [] },
    { name:"Add MoM", accessor:"AddMom", type:"iframe" , subtype:"AddMom", dependents:[], conditions: [{key:'IsSmeFosAgent', value: true}]},
    { name:"View MoM", accessor:"ViewMom", type:"iframe" , subtype:"ViewMom", dependents:[], conditions: [{key:'IsSmeFosAgent', value: true}]},
    { name: "Upload Docs", accessor: "UploadDocs", type: "iframe", subtype: 'upload', dependents: [] },
    {name:"Opt For WA", accessor:"NewButton", type:"iframe" , subtype:"optForWA", dependents:[]},
  ],
}


export const RemarksTable = [
  { name: "S. No", accessor: "", type: "" },
  { name: "EmployeeId", accessor: "EmployeeId", type: "" },
  { name: "Remarks", accessor: "Remarks", type: "" },
  { name: "Status", accessor: "StatusName", type: "" },
  { name: "SubStatus", accessor: "SubStatusName", type: "" },
  { name: "Status Date", accessor: "CreatedOn", type: "datetime" },
];

export const ReferralTable = [
  { name: "S. No", accessor: "", type: "" },
  { name: "LeadID", accessor: "CreatedByLeadID", type: "" },
  { name: "ReferralLead", accessor: "ReferralID", type: "iframe" , show: true, dependents: [] },
  { name: "BookingStatus", accessor: "BookingStatus", type: "" },
];

export const NumberOfPages = [
  { key: 1, value: "10", name: "10" },
  { key: 2, value: "20", name: "20" },
  { key: 3, value: "50", name: "50" },
  { key: 4, value: "100", name: "100" },
  { key: 5, value: "200", name: "200" },
  { key: 6, value: "500", name: "500" },
 
];

export const GetFirstDateOfMonth = ( newDate = new Date()) => {
  try {
    const Today = newDate;
    const d = new Date(Today.getFullYear(), Today.getMonth(), 1);
    const MonthNumber = parseInt(d.getMonth() + 1);
    const Year = d.getFullYear();
    let DateValue = "";
    if (MonthNumber < 10) {
      DateValue = `0${MonthNumber}-01-${Year}`;
    } else {
      DateValue = `${MonthNumber}-01-${Year}`;
    }
    return DateValue;
  } catch (err) {
    return null;
  }
}

export const DateRangeFilter = (Product) => {
  let NumberOfLastMonths = 6;
  if(Product && [115, 7].includes(Product)) {
    NumberOfLastMonths = 12;
  }

  const Result = [];
  const MonthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"];
  const Today = new Date();

  for (let i = NumberOfLastMonths; i > 0; i--) {
    const d = new Date(Today.getFullYear(), Today.getMonth() - i + 1, 1);
    const Month = MonthNames[d.getMonth()];
    const MonthNumber = d.getMonth() + 1;
    const Year = d.getFullYear();
    let DateValue = "";
    if (MonthNumber < 10) {
      DateValue = `0${MonthNumber}-01-${Year}`;
    } else {
      DateValue = `${MonthNumber}-01-${Year}`;
    }
    Result.push({ month: Month, year: Year, value: DateValue });
  }
  Result.reverse();
  return Result;
}

export const ModifyType = (data, type) => {
  let result = data;
  if (type === 'date') {
    result = data ? moment(data).format('DD-MM-YYYY') : "";
  } else if (type === 'datetime') {
    result = data ? moment(data).format('DD-MM-YYYY HH:mm:ss') : "";
  } else if (type === 'int') {
    result = data && data.toFixed(2);
  }

  if (!result) {
    result = "-"
  }
  return result;
}

export const GetTableColumnsInfo = (productId, activeTab,IsFosSMEAgent, activeFilters) => {

  const { IsFOS } = getuser();
  if(productId) {
    const sliderProductkey = ProductMap[productId] || "";
    const sliderFilters = NavBarButtonsSlider[sliderProductkey] || "";
    let tableColumnsProductWise = TableColumns[sliderProductkey] || "";
    tableColumnsProductWise = tableColumnsProductWise?.filter(item => {
      let conditions = item.conditions || []
      if(conditions.length <= 0){
        return true;
      } else {
        let flag = true;
        for (let i = 0; i < conditions.length; i++) {
          const {key, value} = conditions[i];
          switch (key) {
            case 'IsFOS':
             flag = flag && (IsFOS === value)
             break;
            case 'activeTab':
              flag = flag && (activeTab === value)
              break;
            case 'IsSmeFosAgent':
              flag = flag && (IsFosSMEAgent === value);
              break;
            case 'activeFilter':
              flag = flag && (activeFilters.includes(value));
              break;
            default:
              break;
          }
        }
        return flag;
      }
    })
    return {
      SliderFilters:sliderFilters,
      TableColumnsProductWise: tableColumnsProductWise 
    }
  } else {
    return {
      SliderFilters: [],
      TableColumnsProductWise: [] 
    }
  }
 
}

export const GetBmsUrlStatus = async ({ Data }) => {
  try {
    const Url = config.api.base_url + '/common/GetCommonApi/GetBmsUrlStatus';
    let { errorStatus, data } = await GetCommonApiData({
      Url,
      Data: { Type: 'GET_BMS_URL', Data },
      headers: {}
    });
    if (errorStatus === 0) {
      return data;
    } else {
      return null;
    }

  } catch (err) {
    return null
  }
}

export const GetContinureJourneyUrl = async ({ Data }) => {
  try {
    const Url = config.api.base_url + '/common/GetCommonApi/GetContinureJourneyUrl';
    let { errorStatus, data } = await GetCommonApiData({
      Url,
      Data: { Type: 'GET_CONTINUE_JOURNEY_URL', Data },
      headers: {}
    });
    if (errorStatus === 0) {
      return data?.ExitPointURL;
    } else {
      return null;
    }

  } catch (err) {
    return null
  }
}


export const GetVerificationCampaign = async ({ Data }) => {
  try {
    const Url = config.api.base_url + '/common/GetCommonApi/GetVerificationCampaign';
    let { errorStatus, data } = await GetCommonApiData({
      Url,
      Data: { Type: 'GET_VERIFICATION_CAMPAIGN', Data},
      headers: {}
    });
    if (errorStatus === 0) {
      return data;
    } else {
      return null;
    }

  } catch (err) {
    return null
  }
}

export const BmsDocPointUrl = async (DataToSend) => {
  try {
    const Url = config.api.base_url + '/common/PostCommonApi/BmsDocPointUrl';
    let { errorStatus, data } = await PostCommonApiData({
      Url,
      Data: { Type: 'DOC_POINT_BMS_URL', Data: DataToSend },
      headers: {}
    });
    if (errorStatus === 0) {
      const { TokenName, Token, URL } = data;
      setCookie(TokenName, Token, 1);
      return URL || '';
    } else {
      return null;
    }

  } catch (err) {
    return null
  }
}

export const InsertVerificationStatus = async (body) => {
  try {
    const Url = config.api.base_url + '/common/PostCommonApi/InsertVerificationStatus';
    let { errorStatus, data } = await PostCommonApiData({
      Url,
      Data: { Type: 'BMS_VERIFICATION_STATUS', Data: body },
      headers: {}
    });
    if (errorStatus === 0) {
      return data;
    } else {
      return null;
    }

  } catch (err) {
    return null;
  }
}


export const AddCallToQueue = async ({ booking }) => {
  try {
    //const Url = config.api.base_url + '/common/PostCommonApi/AddCallToQueue';
    const lead = {
      "LeadId": booking && booking.BookingID,
      "Name": booking && booking.CustomerName,
      "UserID": parseInt(getuser().UserID),
      "CustomerId": booking && booking.CustomerID,
      "Priority": 0,
      "ProductId": booking && booking.ProductID,
      "Reason": 'BookedLead',
      "ReasonId": 30,
      "CallStatus": "",
      "IsAddLeadtoQueue":1,
      "IsNeedToValidate":0
    }
    var reqData = {
      "UserId": parseInt(getuser().UserID),
      "Leads": [lead]
    };

    // let { errorStatus, data } = await PostCommonApiData({
    //   Url,
    //   Data: { Type: 'ADD_CALL_TO_QUEUE', Data: body },
    //   headers: {}
    // });
    return await ValidateAddLeadToPriorityQueue(reqData , function (resultData)
        {
          try{
            if (resultData != null) {
              if (resultData && resultData.data.data.message && resultData.data.data.message === "Success") {
                  return true;
                  //toast("Lead (" + booking.BookingID + ") Added in Call Queue", { type: 'success' });
              }
              else
              {
                console.log("582");
                return null;
              }
              //toast(`${resultData.message}`, { type: 'error' });
            }
            else
            {
              console.log("58");
              return null;
            }
          }catch(e){
            //toast(`${e}`, { type: 'error' });
          console.log(e)
          return null;
        }
        });

  } catch (err) {
    console.log('errr', err);
    return null
  }
}

export const AddToVerificationQueue = async ({ booking }) => {
  try {
    const Url = config.api.base_url + '/common/PostCommonApi/AddToVerificationQueue';
    const body = {
      "CallbackType": "MatrixFOSVerificationCallback",
      "LeadId": booking && booking.BookingID,
      "ProductId": booking && booking.ProductID,
    }

    let { errorStatus, data } = await PostCommonApiData({
      Url,
      Data: { Type: 'ADD_TO_VERIFICATION_QUEUE', Data: body },
      headers: {}
    });
    if (errorStatus === 0) {
      return data;
    } else {
      return null;
    }

  } catch (err) {
    console.log('errr', err);
    return null
  }
}


export const GetAgentProduct = (props, cb) => {
  try {
    props.GetCommonData({
      root: 'Settings',
      c: 'MATRIX_DASHBOARD_CLIENT',
      con: JSON.stringify({ key: 'MyBookingsProduct' })
    }, function ({ data }) {
      cb(data?.data[0] || []);
    })

  } catch (err) {
    cb([]);
  }
}

export const FilterSearchedTextBookings = ({ Bookings, TextToSearch }) => {
  try {
    const text = TextToSearch.trim();
    const FilteredBookings = Bookings.filter(booking => {
      const CustomerName = (booking?.CustomerName && booking.CustomerName.toLowerCase()) || "";
      const BookingID = (booking?.BookingID) || "";
      const SalesAgent = (booking?.UserName && booking.UserName.toLowerCase()) || "";
      const ApplicationNo = (booking?.ApplicationNo && booking.ApplicationNo.toLowerCase()) || "";
      return (
        CustomerName.includes(text) || (BookingID.toString()).includes(text) ||
        SalesAgent.includes(text) || ApplicationNo.includes(text)
      );
    });
    return FilteredBookings;

  } catch (err) {
    return [];
  }
}

export const GetVerificationStatusUrl = async ({ FilterProductId, Data }) => {
  try {
    let url = "";
    let Details = await GetVerificationCampaign({ Data });
    let Campaign = (Details && Details.Campaign && Details.Campaign.length > 0 ) ? Details.Campaign  : 'preverificationdatacare';
    switch (FilterProductId) {
      case 2:
        const { BookingID, SupplierId, ApplicationNo, EmployeeId, UserID } = Data;
        url = `${config.api.BlockAgent}?u=${UserID}&agent=${EmployeeId}&transfer_agents=&campaign=${Campaign}&bookingid=${BookingID}&transfer_type=verification&dtmf_no=
      &insurerid=${SupplierId}"&application_number=${ApplicationNo}&grade=0`;
        return url;
      default:
        return ""
    }

  } catch (err) {
    return ""
  }
}

export const GetSalesViewurl = async ({ Data, ProductID, IsMobileDevice, BookingType, AgentInfo }) => {
  try {
    let FinalUrl = "", url= "";
    let { userId, token } = JSON.parse(AgentInfo) || {};
    let UserDetails = window.btoa(JSON.stringify({"UserId": userId,"AsteriskToken": token }))
    const {CustomerID, BookingID } = Data;
    
    if (BookingType === 'Primary') {
      url = await LeadContentView(CustomerID, BookingID, ProductID);
      if(IsMobileDevice) {
        let TargetUrl = window.btoa(url);
        FinalUrl = `${config.api.newSalesviewUrl}auth?u=${UserDetails}&t=${TargetUrl}`
      } else {
        FinalUrl = url;
      }
      
    } else if (BookingType === 'Secondary') {
      if(IsMobileDevice) {
        url = await LeadView(CustomerID, BookingID, ProductID, userId);
        let TargetUrl = window.btoa(url);
        FinalUrl = `${config.api.newSalesviewUrl}auth?u=${UserDetails}&t=${TargetUrl}`
      } else {
        url = await LeadView(CustomerID, BookingID, ProductID, userId);
        FinalUrl = url;
      }
    } else {
    }
    return FinalUrl;
  } catch (err) {
    return ""
  }
}

export const GetSearchQueryParams = async ({ ProductDetails }) => {
  try {
    let data = window.btoa(JSON.stringify(ProductDetails));
    return data;
  } catch (err) {
    return {}
  }
}

export const GetHashUrlParameters = () => {
  try {
    const product = localStorage.getItem('MyBookingsProduct');
    let month = localStorage.getItem('MyBookingsMonth');
    if(!month) {
      month = GetFirstDateOfMonth();
    }
    return { Product: product, Month: month };

  } catch (err) {
    console.log(err)
    return {}
  }
}

export const GetBadgeCounterFromBookings = (bookings) => {
  try {
    let Result = [], AllBookingStatus = [], BadgeCounter = {};
    for (let i = 0; i < bookings.length; i++) {
      const element = bookings[i];
    
      let BookingStatusType = JSON.parse(element.BookingStatusType);
  
      AllBookingStatus = [...AllBookingStatus, ...BookingStatusType]
    }
    
    for (let i in AllBookingStatus) {
      BadgeCounter[AllBookingStatus[i]] = BadgeCounter[AllBookingStatus[i]] ? BadgeCounter[AllBookingStatus[i]] + 1 : 1;
    }

    for (let key in BadgeCounter) {
      let data = { BookingStatusType: key, Count: BadgeCounter[key] };
      Result.push(data);
    }

    return Result;
  } catch (err) {
    console.log('Inside GetBadgeCounterFromBookings', err);
    return []
  }
}

export const GetBadgeCounterFromBookings_V2 = (bookings) => {
  try {
    let Result = [], AllBookingStatus = [], BadgeCounter = {};
    for (let i = 0; i < bookings.length; i++) {
      const element = bookings[i];
    
      let BookingStatusType = element.BookingStatusType;
  
      AllBookingStatus = [...AllBookingStatus, ...BookingStatusType]
    }
    
    for (let i in AllBookingStatus) {
      BadgeCounter[AllBookingStatus[i]] = BadgeCounter[AllBookingStatus[i]] ? BadgeCounter[AllBookingStatus[i]] + 1 : 1;
    }

    for (let key in BadgeCounter) {
      let data = { BookingStatusType: key, Count: BadgeCounter[key] };
      Result.push(data);
    }

    return Result;
  } catch (err) {
    console.log('Inside GetBadgeCounterFromBookings', err);
    return []
  }
}

export const PerformActionsOnLinkClick = async ({ data, col, FilterProductId }) => {
  let ReturnUrl = 0, Data = "", ErrorStatus = 0, AdditionalInfo = {};
  try {
    let url = '';
    switch (col?.name) {
      case 'BookingID':
        url = LeadContentView(data?.CustomerID, data?.BookingID, FilterProductId);
        ReturnUrl = 1;
        break;

      case 'Status':
        url = await GetBmsUrlStatus({ BookingID: data.BookingID });
        ReturnUrl = 1;
        break;

      case 'ContinueJourney':
        url = data[col.accessor];
        ReturnUrl = 1;
        break;

      case 'PIVCLink':
        url = data[col.accessor];
        ReturnUrl = 1;
        break;

      case 'MobileNumber':
        let response = await AddCallToQueue({ booking: data });
        console.log("790" + response);
        if (response) {
          Data = `Lead added on queue successfully: ${data?.BookingID}`;
          ErrorStatus = 0;
        } else {
          Data = `Failed to add lead on queue`;
          ErrorStatus = 1;
        }
        break;

      case 'Upload Docs':
        url = await BmsDocPointUrl(data);
        ReturnUrl = 1;
        break;

      case 'AddToVerificationCallQueue':
        let verificationResponse = await AddToVerificationQueue({ booking: data });
        if (verificationResponse.IsSuccess) {
          Data = `${verificationResponse.Message}: ${data?.BookingID}`
          ErrorStatus = 0;

          let verificationQueueInfo = JSON.parse(localStorage.getItem(VERIFICATION_ADDED_LEADS)) || [];
          verificationQueueInfo = [
            ...verificationQueueInfo,
            {createdAt: moment().format("YYYY-MM-DD HH:mm:ss"), LeadId: data?.BookingID}
          ]
          localStorage.setItem(VERIFICATION_ADDED_LEADS, JSON.stringify(verificationQueueInfo))
          AdditionalInfo = {...AdditionalInfo, verificationQueueInfo}

        } else {
          Data = `${verificationResponse.Message}: ${data?.BookingID}`
          ErrorStatus = 1;
        }
        break; 

      default:
        break;
    }

    if (ReturnUrl && url) {
      window.open(url);
    }
    return { ErrorStatus, Data, AdditionalInfo}

  } catch (err) {
    return { ErrorStatus: 1, Data: null, AdditionalInfo}

  }
}

export const GetMyBookingsBmsApi = async (body) => {
  try {
    const ManagerIds = body?.ManagerIds || '';
    const root = !ManagerIds ? 'GetMyBookingsBmsApiAgent' : 'GetMyBookingsBmsApiTL'
    const Url = config.api.base_url + '/common/PostCommonApi/'+root;
    let { errorStatus, data, info } = await PostCommonApiData({
      Url,
      Data: { Type: 'MY_BOOKINGS_BMS_API', Data: body, Timeout: 10000 },
      headers: {}
    });
if (errorStatus === 0) {
      return { data, info};
    } else {
      return null;
    }

  } catch (err) {
    return null
  }
}


export const GetMyBookingsBmsApi2 = async (body) => {
  try {
    const ManagerIds = body?.ManagerIds || '';
    const root = !ManagerIds ? 'GetMyBookingsBmsApiAgent' : 'GetMyBookingsBmsApiTL'
    const Url = config.api.base_url + '/common/PostCommonApiV2/'+root;
    let { errorStatus, data, info } = await PostCommonApiData({
      Url,
      Data: { Type: 'MY_BOOKINGS_BMS_API2', Data: body, Timeout: 10000 },
      headers: {}
    });
if (errorStatus === 0) {
      return { data, info};
    } else {
      return null;
    }

  } catch (err) {
    return null
  }
}


export const GetOptForWA= async (CustomerId)=>{
  try {
    const Url = config.api.base_url + '/common/GetCommonApi/CustomerOptForWA';
    let { errorStatus, data } = await GetCommonApiData({
      Url,
      Data: { Type: 'OPT_FOR_WA',  Data: CustomerId },
      headers: {}
    });
    // console.log("The errorStatus is ",errorStatus);
    // console.log("The data is ",data);
    if (errorStatus === 0) {
      if(data[0] && data[0].hasOwnProperty('IsAllowed'))
      {
           return data[0].IsAllowed;
      }
      else{
         return false;
      }
    } else {
      return null;
    }

  } catch (err) {
    return null
  }
 
}

export const GetBookingById = async (body) => {
  const Url = config.api.base_url + '/MyBooking/getBookingById';
  try {
    let response = await axios.post(Url,body);
    if ([200, 201].includes(response?.data?.status)) {
      return {
        data: response?.data?.data || "",
        info: response?.data?.info || "{}",
        isError: false,
        errorMessage: ""
      }
    } else {
      return {
        data: "",
        info: "{}",
        isError: response?.response?.data.isError || false,
        errorMessage : response?.response?.data.errorMessage || ""
      }
    }
  } catch (err) {
    return {
      data: "",
      info: "{}",
      isError: true,
      errorMessage : err
    }
  }
}

export const SendAutoDebitPrompt= async (Booking)=>{
  try {
    const Url = config.api.base_url + '/common/AutoDebitPrompt';
    let result= await axios.get(Url, {
      params:  { Type: 'AUTODEBIT_PROMPT', Data: Booking }
    }
    );
    // console.log("The errorStatus is ",errorStatus);
    // console.log("The data is ",data);

    if(result.status==200 && !result.data.errorStatus)
    {
        return true;
    }
    else{
        return false;
    }
    

  } catch (err) {
    return false
  }

  
 
}
