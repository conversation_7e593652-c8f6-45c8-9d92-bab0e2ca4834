import React, { useEffect, useState } from "react";
import { LotteryData } from "../../store/actions/CommonAction";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './StarsAward.scss';
import _ from 'underscore';
import { useHistory } from 'react-router-dom';
import lottieImage from "./Lottie.json";
import Loading from "../../Loading";
import Lottie from "lottie-react";
import { JAG_BU_REWARD_INFO } from "./constants";
import JagCountTimer from "./JagCountTimer";

const LotteryJag = (props) => {

    const [ticketNumber, setTicketNumber] = useState('');
    const [loading, setLoading] = useState(true);
    const [IsFetchJagWinner, setIsFetchJagWinner] = useState(false);
    const [IsCountDown, setIsCountDown] = useState(false);

    const [winnerDetails, setWinnerDetails] = useState({});
    const [currentRewardNo, setCurrentRewardNo] = useState('');
    // const [tenure, setTenure] = useState('');
    const resultStatusCode = winnerDetails?.StatusCode || 0;

    // const updateWinnerTenure = (data) => {
    //     let today = new Date();
    //     let doj = new Date(data?.DateOfJoining);
    //     let diff = parseInt(today.getTime() - doj.getTime());
    //     let div = 1000 * 60 * 60 * 24 * 365;
    //     let time = parseFloat(diff) / parseFloat(div);
    //     setTenure(time);
    // };
    const fetchWinnerDetails = () => {
        let checkTicketRange = parseInt(ticketNumber);
        if (checkTicketRange <= 0 || ticketNumber && ticketNumber?.length != 4 || isNaN(ticketNumber)) {
            toast("Please Input a valid ticket Number", { type: "error" });
            return;
        }
        //setLoading(true);
        LotteryData({
            root: "getWinnerDetailJag",
            method: "post",
            params: {
                BUId: JAG_BU_REWARD_INFO.BUId,
                RewardId: JAG_BU_REWARD_INFO.RewardId,
                TicketNumber: ticketNumber,
                Version: JAG_BU_REWARD_INFO.Version
            }
        }, (result) => {
            if (result.data && result.data.status == 200) {
                let resData = result?.data?.data || {};
                setWinnerDetails(resData);
                // updateWinnerTenure(resData);
                if(resData?.StatusCode === 200) {
                    setIsCountDown(true);
                    setTimeout(()=> {
                        setIsCountDown(false);
                        setIsFetchJagWinner(true);
                    }, 3000);
                } else {
                    setIsFetchJagWinner(true);
                }
            }
          //  setLoading(false);
            return;
        }, 'post');
    }

    const getRewardDetails = ({ BUId, RewardId }) => {
        debugger
        LotteryData({
            root: "GetRewardDetails",
            params: { BUId, RewardId }
        }, (result) => {
            debugger
            if (result?.data && result?.data?.status == 200) {
                let resData = result?.data?.data[0][0] || [];
                let count = Math.abs(resData?.MaxRewards - resData?.PendingRewards) + 1;
                setCurrentRewardNo(count);
            }
            return;
        });
    }

    const handleNavigateWelcome = () => {
        props.handleBacktoHome();
    }

    const handleChangeTicketNumber = (e) => {
        let ticket = e.target.value.trim();
        if (ticket.length <= 4) {
            setTicketNumber(ticket);
        }
    }

    useEffect(() => {
        getRewardDetails({
            BUId: JAG_BU_REWARD_INFO.BUId,
            RewardId: JAG_BU_REWARD_INFO.RewardId,
        });

    }, []);

    // const ape = parseFloat((winnerDetails?.IssuedAPE_CurrYear || 0) + (winnerDetails?.SecondaryIssuedAPE || 0)) / 10000000;
    const ape = parseFloat(winnerDetails?.ProjectedIssuedAPE_CurrYear || 0) / 10000000;
    const issuedApe = ape && ape.toFixed(1) || '';

    return (
        <div className="EmergingStarsLayout">
            <ToastContainer />
            {!IsFetchJagWinner ?
                <>
                {IsCountDown &&  <JagCountTimer />}
                    <div className="BoxLayout">
                        <button onClick={handleNavigateWelcome} className="backButton">BACK</button>
                        <div className="Header">
                            <img src="/lottery/Award/jagLogo.png" />
                            <div className="star">
                                <img src="/lottery/Award/star.png" />
                            </div>
                            <img src="/lottery/Award/pbLogo.svg" width="180px" />
                        </div>
                        <div className="LotteryTicketPage">
                            <img src="/lottery/Award/homeTicket.svg" />
                            <h1>JAG LOTTERY EVENT</h1>
                            <h4>HOUSE #{currentRewardNo} GOES TO</h4>
                            <fieldset>
                                <legend>Ticket Number:</legend>
                                <input type="text" id="fname" name="fname" onChange={handleChangeTicketNumber} value={ticketNumber} autoComplete="off"/>
                            </fieldset>
                            <button onClick={fetchWinnerDetails}>GO</button>
                        </div>

                    </div>
                    <img src="/lottery/Award/giftimage.png" className="giftbgImage" />
                </>

                :
                <>
                    {/* resultStatusCode === 200 for winning ticket success */}
                    {
                        resultStatusCode === 200 &&
                        <div className="BoxLayout">
                            <button onClick={handleNavigateWelcome} className="backButton">BACK</button>
                            <div className="Header">
                                <img src="/lottery/Award/jagLogo.png" />
                                <div className="star">
                                    <img src="/lottery/Award/star.png" />
                                    <h3>JAG WINNER</h3>
                                </div>
                                <img src="/lottery/Award/pbLogo.svg" width="180px" />
                            </div>

                            <div className="LeftLayout">
                                <div className="circle">
                                    {/* <img src="/lottery/Award/Home.png" /> */}
                                    <Lottie animationData={lottieImage} className="lottieAnimation" loop={true} />
                                    <img src="/lottery/Award/House_gift.png" />
                                </div>
                            </div>
                            <div className="RightLayout">
                                <div className="text-center"><img src="/lottery/Award/congratulation.svg" /></div>
                                {/* <fieldset>
                                <legend>Ticket Number:</legend>
                                <input type="text" id="fname" name="fname" disabled value={ticketNumber} />
                            </fieldset>
                            {/* <button>GO</button> */}
                                <div className="EmployeeName">{winnerDetails?.UserName || ''}
                                {winnerDetails?.EmployeeId && <p>({winnerDetails?.EmployeeId || ''})</p>}
                                </div>
                                <ul>

                                    <li>Location
                                        <strong>{winnerDetails?.Location || ''}</strong>
                                    </li>
                                    <li>BU
                                        <strong>{winnerDetails?.BU || ''}</strong>
                                    </li>
                                    <li>Ticket Number
                                        <strong>{ticketNumber}</strong>
                                    </li>
                                    {winnerDetails?.ProductID === 117 && [65, 67].indexOf(winnerDetails?.BuId) !== -1 ?
                                            <li className="APEIssue">Issued Bookings
                                                <strong>{winnerDetails?.ProjectedIssuedAPE_CurrYear || ''}</strong>
                                            </li>
                                            :
                                            <li className="APEIssue">Issued APE
                                                <strong>{issuedApe} CR</strong>
                                            </li>
                                        }
                                </ul>
                            </div>
                        </div>
                    }
                    {/* resultStatusCode === 405 for alreadya winner */}
                    {/* resultStatusCode === 404 for invalid ticket */}
                    {[404, 405].includes(resultStatusCode) &&
                        <>
                            <div className="BoxLayout">
                                <button onClick={handleNavigateWelcome} className="backButton">BACK</button>
                                <div className="Header">
                                    <img src="/lottery/Award/jagLogo.png" />
                                    <div className="star">
                                        <img src="/lottery/Award/star.png" />
                                    </div>
                                    <img src="/lottery/Award/pbLogo.svg" width="180px" />
                                </div>
                                <div className="LotteryTicketPage">
                                    <img src="/lottery/Award/carTicket.svg" />

                                    {resultStatusCode === 404 && <>
                                        <h1> Oops !! Invalid Ticket</h1>
                                        <h1> Re-spin the Wheels</h1>
                                    </>}
                                    {resultStatusCode === 405 && <>
                                        <h1> Advisor has already Won !!</h1>
                                        <h1 style={{ fontSize: '40px' }}> Once a winner always a winner</h1>
                                        <h1 style={{ fontSize: '40px' }}> Re-spin the Wheels</h1>
                                    </>}
                                </div>
                            </div>
                            <img src="/lottery/Award/giftimage.png" className="giftbgImage" />
                        </>
                    }
                </>
            }
        </div >
    );

}

export default LotteryJag;

