import React, { useEffect, useState } from "react";
import { FetchHealthRenewalPaymentLinkService, UpdateMISPaymentRejection, IsUserAttributesAuthorized, FetchHealthRenewalNeedAnalysis, SetHealthRenewalNeedAnalysis } from "../../store/actions/CommonAction";
import {
    Card,
    CardHeader,
    CardBody,
    CardTitle,
    Row,
    Col
} from "reactstrap";
import { Form, Table, Modal } from 'react-bootstrap';
import { TableContainer, TableHead, TableCell, TableRow, TableBody, Button, ButtonGroup, Checkbox, FormControlLabel, Menu, MenuItem } from "@mui/material";
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import PaymentLinkCreation from "./PaymentLinkCreation";
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import ExcelJS from 'exceljs';
import { saveAs } from 'file-saver';
import { getuser } from "utility/utility";
import Datetime from 'react-datetime';
import moment from 'moment';

const MISPaymentLinkRequest = () => {
    const [data, setData] = useState([]);
    const [LeadId, setLeadId] = useState(0);
    const [UpdateLeadId, setUpdateLeadId] = useState(0);
    const [Id, setId] = useState(0);
    const [CreationPageOpen, setCreationPageOpen] = useState(false);
    const [RejectPageOpen, setRejectPageOpen] = useState(false);
    const [RequestOpen, setRequestOpen] = useState(false);
    const [paymentMode, setPaymentMode] = useState(0);
    const [noCostEMI, setNoCostEMI] = useState(false);
    const [nceFrequency, setNceFrequency] = useState([]);
    const [bypassNCE, setBypassNCE] = useState(false);
    const [Remarks, setRemarks] = useState('');
    const [SelectedFile, setSelectedFile] = useState();
    const [RequestLogOpen, setRequestLogOpen] = useState(false);
    const [dateFrom, setDateFrom] = React.useState(moment().format("YYYY-MM-DD"));
    const [dateTo, setDateTo] = React.useState(moment().format("YYYY-MM-DD"));
    const [IsAuthorized, setIsAuthorized] = useState(false);
    const [IsAuthorizedToView, setIsAuthorizedToView] = useState(false);
    const [anchorEl, setAnchorEl] = useState(null);
    const [isRenewalLead, setisRenewalLead] = useState(false);

    useEffect(() => {
        FetchHealthRenewalPaymentLinkService({ Type: 5 }, function (resultData) {
            if (resultData && resultData.data.data && Array.isArray(resultData.data.data[0])) {
                setData(resultData.data.data[0]);
            }
        })

        IsUserAttributesAuthorizedMethod();
        IsUserAttributesAuthorizedMethodToView();
    }, []);

    const handleCreationPageOpen = (LeadId, Id) => {
        setLeadId(LeadId);
        setCreationPageOpen(true);
        setId(Id);
    }

    const handleCreationPageClose = () => {
        setCreationPageOpen(false);
        setLeadId(0);
        setId(0);
        window.location.reload();
    }

    const handleRejectOpen = (LeadId, Id) => {
        setLeadId(LeadId);
        setRejectPageOpen(true);
        setRemarks('');
        setId(Id);
    }

    const handleRejectClose = () => {
        setRejectPageOpen(false);
        setLeadId(0);
        setId(0);
        setSelectedFile();
    }

    const handleRemarksChange = (e) => {
        setRemarks(e.target.value);
    }

    const handleSubmitButton = () => {
        let formData = new FormData();

        var data = {
            Id: Id,
            CancellationReason: Remarks,
            UploadedFile: SelectedFile,
        };

        Object.keys(data).map((key) => {
            formData.append(key, data[key]);
        })

        UpdateMISPaymentRejection(formData, getuser().UserID, function (requestData) {
            if (requestData) {
                setSelectedFile();
                toast(requestData.data.Data, { type: 'success' });
                FetchHealthRenewalPaymentLinkService({ Type: 5 }, function (resultData) {
                    if (resultData && resultData.data.data && Array.isArray(resultData.data.data[0])) {
                        setData(resultData.data.data[0]);
                    }
                })
            }
        })

        handleRejectClose();
    }

    const handleFileChange = (event) => {
        let fileName = event.target.files[0].name;
        if (isImage(fileName)) {
            setSelectedFile(event.target.files[0])
        }
        else {
            toast("Please upload an image file.", { type: 'error' });
        }
    };

    const isImage = (filename) => {
        var ext = getExtension(filename);
        switch (ext.toLowerCase()) {
            case "png":
            case "jpg":
            case "jpeg":
                return true;
            default:
                return false;
        }
    }

    const getExtension = (filename) => {
        var parts = filename.split(".");
        return parts[parts.length - 1];
    }

    const handleRequestLogOpen = () => {
        setRequestLogOpen(true);
    }
    const handleRequestrequestopen = () => {
        setRequestOpen(true);
    }

    const handleRequestLogClose = () => {
        setRequestLogOpen(false);
        setDateFrom(moment().format("YYYY-MM-DD"))
        setDateTo(moment().format("YYYY-MM-DD"))
    }

    const handleDateFromChange = (event) => {
        setDateFrom(event.format("YYYY-MM-DD"));
    };
    const handleDateToChange = (event) => {
        setDateTo(event.format("YYYY-MM-DD"));
    };

    const handleApply = () => {
        let temp = []
        FetchHealthRenewalPaymentLinkService({ FromDate: dateFrom, ToDate: dateTo, Type: 7 }, function (resultData) {
            if (resultData && resultData.data.data && Array.isArray(resultData.data.data[0])) {
                if (resultData.data.data[0].length > 0) {
                    DownloadData(resultData.data.data[0]);
                }
            }
        })
    };

    const DownloadData = async (data) => {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Sheet1');

        worksheet.columns = [
            { header: "LeadID", key: "LeadID", width: 10 },
            { header: "InsurerName", key: "InsurerName", width: 10 },
            { header: "PremiumAmount", key: "PremiumAmount", width: 10 },
            { header: "PaymentReason", key: "PaymentReason", width: 10 },
            { header: "RequestedAgentID", key: "RequestAgentID", width: 10 },
            { header: "RequestedAgentName", key: "RequestedAgent", width: 10 },
            { header: "CreatedOn", key: "CreatedOn", width: 10 },
            { header: "L2ApprovalReasonID", key: "L2ApprovalReasonID", width: 10 },
            { header: "RequestStatus", key: "Status", width: 10 },
            { header: "LinkCreatedBy", key: "LinkCreatedBy", width: 10 },
            { header: "LinkCreatedOn", key: "LinkCreatedOn", width: 10 },
            { header: "CancellationReason", key: "CancellationReason", width: 10 },
            { header: "MISAgent", key: "MISAgent", width: 10 },
            { header: "MISReceivedDate", key: "MISReceivedDate", width: 10 }
        ];

        data.forEach((item) => {
            worksheet.addRow(item);
        });

        const buffer = await workbook.xlsx.writeBuffer();
        saveAs(new Blob([buffer], { type: 'application/octet-stream' }), `Data from ${ExtractDate(dateFrom)} to ${ExtractDate(dateTo)}.xlsx`);
    }

    const ExtractDate = (Inputdate) => {
        const date = new Date(Inputdate);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const formattedDate = `${day}/${month}/${year}`;
        return formattedDate;
    }

    const IsUserAttributesAuthorizedMethod = () => {
        IsUserAttributesAuthorized({ AttributeId: 17 }).then(function (data) {
            if (data.data.data.Authorized) {
                setIsAuthorized(true);
                return true;
            }
            else {
                setIsAuthorized(false);
                return false
            }
        }).catch((result) => {
            setIsAuthorized(false);
            return false;
        })
    }

    const IsUserAttributesAuthorizedMethodToView = () => {
        IsUserAttributesAuthorized({ AttributeId: 18 }).then(function (data) {
            if (data.data.data.Authorized) {
                setIsAuthorizedToView(true);
                return true;
            }
            else {
                setIsAuthorizedToView(false);
                return false
            }
        }).catch((result) => {
            setIsAuthorizedToView(false);
            return false;
        })
    }

    const handleRequestClose = () => {
        setRequestOpen(false);
        setPaymentMode(0);
        setNoCostEMI(false);
        setNceFrequency([]);
        setBypassNCE(false);
        setUpdateLeadId(undefined)
        setisRenewalLead(false);
    }

    const handlePaymentModeChange = (e) => {
        setPaymentMode(e.target.value);
    }

    const handleNoCostEMIChange = () => {
        if (noCostEMI == 1) {
            setNceFrequency([]);
            setBypassNCE(null);
        }
        setNoCostEMI(!noCostEMI);
    }

    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleNCEFrequencyChange = (value) => {
        const currentIndex = nceFrequency.indexOf(value);
        const newChecked = [...nceFrequency];

        if (currentIndex === -1) {
            newChecked.push(value);
        } else {
            newChecked.splice(currentIndex, 1);
        }

        setNceFrequency(newChecked);
    };

    const handleBypassNCEChange = () => {
        setBypassNCE(!bypassNCE);
    }

    const handleUpdatePayment = () => {
        var data = {
            LeadID: parseInt(UpdateLeadId, 10),
            PaymentMode: parseInt(paymentMode, 10),
            NoCostEMI: noCostEMI ? 1 : 0,
            BypassNCE: bypassNCE,
            NceFrequency: nceFrequency.filter(Boolean).join(","),
        };

        SetHealthRenewalNeedAnalysis(data, function (requestData) {
            if (requestData && requestData.data.Data == true) {
                toast("Data updated.", { type: 'success' });
                handleFetchDetails();
            }
            else {
                toast("Error occured.", { type: 'error' });
            }
        })
    }

    const handleFetchDetails = () => {
        FetchHealthRenewalNeedAnalysis(UpdateLeadId, getuser().UserID, function (requestData) {
            if (requestData) {
                var data = (requestData.data.Data);
                console.log(data);
                if (data) {
                    if (data.LeadSource == 'Renewal') {
                        setisRenewalLead(true);
                    }
                    else {
                        toast("Not Renewal Lead.", { type: 'error' });
                        setisRenewalLead(false);
                    }
                    setPaymentMode(data.PaymentMode ? data.PaymentMode : 0);
                    setNoCostEMI(data.NoCostEMI ? data.NoCostEMI - 1 : 0)
                    setBypassNCE(data.ByPassNCE ? data.ByPassNCE : false);
                    setNceFrequency(data.NCEFrequency ? data.NCEFrequency.split(",") : []);
                }
                else {
                    setPaymentMode(0);
                    setNoCostEMI(false);
                    setNceFrequency([]);
                    setBypassNCE(false);
                    toast("No Data available.", { type: 'error' });
                }
            }

        })
    }

    return (
        <div>
            <Row>
                <Col md="12">
                    <Form>
                        <Card>
                            <CardHeader>
                                <Row>
                                    <Col md={IsAuthorized == true ? 4 : 10}>
                                        <CardTitle tag="h4">Payment Link Request</CardTitle>
                                    </Col>
                                    <Col md={IsAuthorized == true ? 4 : 2}>
                                        <Button className="btn btnSmall"
                                            color="primary"
                                            onClick={handleRequestrequestopen}
                                        >
                                            update payment link
                                        </Button>
                                    </Col>
                                    {IsAuthorized == true ? <Col md={4}>
                                        <Button className="btn btnSmall"
                                            color="primary"
                                            onClick={handleRequestLogOpen}
                                        >
                                            Request Logs
                                        </Button>
                                    </Col> : <></>}
                                </Row>
                            </CardHeader>
                            <CardBody>
                                {data && IsAuthorizedToView == true && Array.isArray(data) && data.length > 0 ?
                                    <div className="RenewalPaymentLink">
                                        <br />
                                        <TableContainer className="w-full rounded-lg shadow-md">
                                            <Table striped bordered hover>
                                                <TableHead>
                                                    <TableRow>
                                                        <TableCell align="center">Lead ID</TableCell>
                                                        <TableCell align="center">Proposer Name</TableCell>
                                                        <TableCell align="center">Amount</TableCell>
                                                        <TableCell align="center">Policy Number</TableCell>
                                                        <TableCell align="center">Pehchan ID</TableCell>
                                                        <TableCell align="center">Reason</TableCell>
                                                        <TableCell align="center">Action</TableCell>
                                                    </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                    {data.map((row) => (
                                                        <TableRow>
                                                            <TableCell align="center">
                                                                {row.LeadID ? row.LeadID : "N/A"}
                                                            </TableCell>
                                                            <TableCell align="center">
                                                                {row.ProposerName ? row.ProposerName : "N/A"}
                                                            </TableCell>
                                                            <TableCell align="center">
                                                                {row.PremiumAmount ? row.PremiumAmount : "N/A"}
                                                            </TableCell>
                                                            <TableCell align="center">
                                                                {row.PolicyNo ? row.PolicyNo : "N/A"}
                                                            </TableCell>
                                                            <TableCell align="center">
                                                                {row.PehchanID ? row.PehchanID : "N/A"}
                                                            </TableCell>
                                                            <TableCell align="center">
                                                                {row.PaymentReason ? row.PaymentReason : "N/A"}
                                                            </TableCell>
                                                            <TableCell align="center">
                                                                <ButtonGroup>
                                                                    <Button
                                                                        id="btnstaging"
                                                                        className="btn btnSmall"
                                                                        color="success" variant="contained"
                                                                        onClick={() => { handleCreationPageOpen(row.LeadID, row.Id) }}
                                                                    >
                                                                        Create Link
                                                                    </Button>
                                                                </ButtonGroup> &nbsp;
                                                                <ButtonGroup>
                                                                    <Button
                                                                        id="btnstaging"
                                                                        className="btn btnSmall"
                                                                        color="error" variant="contained"
                                                                        onClick={() => { handleRejectOpen(row.LeadID, row.Id) }}
                                                                    >
                                                                        Reject
                                                                    </Button>
                                                                </ButtonGroup>
                                                            </TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>

                                            </Table>
                                        </TableContainer>
                                    </div>
                                    :
                                    <Row>
                                        <Col>
                                            <br />
                                            <h5 className="text-danger">No Data Found!</h5>
                                        </Col>
                                    </Row>
                                }
                            </CardBody>
                        </Card>
                    </Form>
                </Col>
            </Row>
            <div className="content">
                <ToastContainer />
                <Modal
                    show={CreationPageOpen}
                    onHide={handleCreationPageClose}
                    dialogClassName="modal-90w"
                >
                    <Modal.Header closeButton>
                        <Modal.Title>Create Payment Link</Modal.Title>
                    </Modal.Header>
                    <Modal.Body className="uploadLeadPopup">
                        <PaymentLinkCreation LeadId={LeadId} Id={Id} onClose={handleCreationPageClose} />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={handleCreationPageClose}>
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            </div>
            <div className="content">
                <ToastContainer />
                <Modal
                    show={RejectPageOpen}
                    onHide={handleRejectClose}
                    dialogClassName="modal-90w"
                >
                    <Modal.Header closeButton>
                        <Modal.Title>Payment Rejection</Modal.Title>
                    </Modal.Header>
                    <Modal.Body className="uploadLeadPopup">
                        <Form>
                            <Form.Group as={Col} md={12} >
                                <Form.Label className="text-primary-dark">Remarks</Form.Label>
                                <Form.Control
                                    as="textarea"
                                    rows={5}
                                    value={Remarks}
                                    onChange={handleRemarksChange}
                                />
                            </Form.Group>
                            <br />
                            <div className="d-flex justify-content-between">
                                <div>
                                    <input
                                        type="file"
                                        id="hiddenFileInput"
                                        onChange={handleFileChange}
                                        style={{ display: "none" }}
                                    />
                                    <label htmlFor="hiddenFileInput">
                                        <Button component="span" color="warning" variant="contained">
                                            Upload File
                                        </Button>
                                    </label>
                                    {SelectedFile && SelectedFile.name ?
                                        <label>
                                            &nbsp;
                                            <label><h6>Uploaded Attachment : &nbsp;</h6></label>
                                            <label>{SelectedFile.name}</label>
                                        </label> : <></>}
                                </div>
                                <ButtonGroup>
                                    <Button
                                        id="btnstaging"
                                        color="error" variant="contained"
                                        disabled={Remarks.trim().length == 0}
                                        onClick={handleSubmitButton}
                                    >
                                        Submit
                                    </Button>
                                </ButtonGroup>
                            </div>
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={handleRejectClose}>
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            </div>
            <div className="content">
                <ToastContainer />
                <Modal
                    show={RequestLogOpen}
                    onHide={handleRequestLogClose}
                    dialogClassName="modal-90w"
                >
                    <Modal.Header closeButton>
                        <Modal.Title>Request Logs</Modal.Title>
                    </Modal.Header>
                    <Modal.Body className="uploadLeadPopup">
                        <Row>
                            <Col md={4}>
                                <label>From</label>
                                <Datetime
                                    dateFormat="YYYY-MM-DD"
                                    value={dateFrom}
                                    label="From Date"
                                    name="from"
                                    onChange={moment => handleDateFromChange(moment)}
                                    utc={true}
                                    timeFormat={false} />
                            </Col>
                            <Col md={4}>
                                <label>To</label>
                                <Datetime
                                    dateFormat="YYYY-MM-DD"
                                    value={dateTo}
                                    label="To Date"
                                    name="to"
                                    onChange={moment => handleDateToChange(moment)}
                                    utc={true}
                                    timeFormat={false} />
                            </Col>
                        </Row>
                        <br />
                        <Row>
                            <Col>
                                <Button variant="contained" onClick={handleApply}>
                                    Download Data
                                </Button>
                            </Col>
                        </Row>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="secondary" onClick={handleRequestLogClose}>
                            Close
                        </Button>
                    </Modal.Footer>
                </Modal>
            </div>
            <div className="content">
                <ToastContainer />
                <Modal
                    show={RequestOpen}
                    onHide={handleRequestClose}
                    dialogClassName="modal-90w"
                >
                    <Modal.Header closeButton>
                        <Modal.Title>Update Payment Mode</Modal.Title>
                    </Modal.Header>
                    <Modal.Body className="uploadLeadPopup">
                        <Form>
                            <Row className="mb-3">
                                <Col md={6}>
                                    <Form.Group>
                                        <Form.Label className="text-primary-dark">LeadID</Form.Label>
                                        <Form.Control
                                            type="text"
                                            value={UpdateLeadId ? UpdateLeadId : ""}
                                            onChange={e => {
                                                const val = e.target.value.replace(/[^0-9]/g, "");
                                                setUpdateLeadId(val);
                                            }}
                                            inputMode="numeric"
                                            pattern="[0-9]*"
                                            autoComplete="off"
                                        />
                                    </Form.Group>
                                </Col>
                                <Col md={6}>
                                    <Form.Group>
                                        <Form.Label className="text-primary-dark">&nbsp;</Form.Label>
                                        <Button
                                            variant="outlined"
                                            onClick={handleFetchDetails}
                                            style={{ marginTop: '32px' }}
                                        >
                                            Fetch
                                        </Button>
                                    </Form.Group>
                                </Col>
                            </Row>
                            {isRenewalLead == true ?
                                <>
                                    <Row className="mb-3">
                                        <Col md={6}>
                                            <Form.Group>
                                                <Form.Label className="text-primary-dark">Payment Mode</Form.Label>
                                                <Form.Select
                                                    value={paymentMode}
                                                    onChange={handlePaymentModeChange}
                                                >
                                                    <option value="0">NA</option>
                                                    <option value="1">Monthly</option>
                                                    <option value="2">Quarterly</option>
                                                </Form.Select>
                                            </Form.Group>
                                        </Col>
                                        <Col md={6} className="d-flex align-items-center">
                                            <Form.Group>
                                                <Form.Label className="text-primary-dark me-3">NoCost EMI</Form.Label>
                                                <Form.Check
                                                    type="switch"
                                                    checked={noCostEMI}
                                                    onChange={handleNoCostEMIChange}
                                                    inline
                                                />
                                            </Form.Group>
                                        </Col>
                                    </Row>
                                </>
                                : <></>
                            }

                            {noCostEMI == 1 && <>
                                <Row className="mb-3">
                                    <Col md={6}>
                                        <Form.Group>
                                            <Form.Label className="text-primary-dark">NCE Frequency</Form.Label>
                                            <div>
                                                <Button
                                                    variant="outlined"
                                                    onClick={handleClick}
                                                    endIcon={<KeyboardArrowDownIcon />}
                                                    className="w-100 text-start"
                                                >
                                                    {nceFrequency.length > 0
                                                        ? `Selected (${nceFrequency.filter(Boolean).join(",")})`
                                                        : "Select NCE Frequency"}
                                                </Button>
                                                <Menu
                                                    anchorEl={anchorEl}
                                                    open={Boolean(anchorEl)}
                                                    onClose={handleClose}
                                                    PaperProps={{
                                                        style: {
                                                            maxHeight: 300,
                                                            width: '250px',
                                                        },
                                                    }}
                                                >
                                                    <MenuItem>
                                                        <FormControlLabel
                                                            control={
                                                                <Checkbox
                                                                    checked={nceFrequency.includes("9")}
                                                                    onChange={() => handleNCEFrequencyChange("9")}
                                                                />
                                                            }
                                                            label="9"
                                                        />
                                                    </MenuItem>
                                                    <MenuItem>
                                                        <FormControlLabel
                                                            control={
                                                                <Checkbox
                                                                    checked={nceFrequency.includes("12")}
                                                                    onChange={() => handleNCEFrequencyChange("12")}
                                                                />
                                                            }
                                                            label="12"
                                                        />
                                                    </MenuItem>
                                                    <MenuItem>
                                                        <FormControlLabel
                                                            control={
                                                                <Checkbox
                                                                    checked={nceFrequency.includes("18")}
                                                                    onChange={() => handleNCEFrequencyChange("18")}
                                                                />
                                                            }
                                                            label="18"
                                                        />
                                                    </MenuItem>
                                                    <MenuItem>
                                                        <FormControlLabel
                                                            control={
                                                                <Checkbox
                                                                    checked={nceFrequency.includes("24")}
                                                                    onChange={() => handleNCEFrequencyChange("24")}
                                                                />
                                                            }
                                                            label="24"
                                                        />
                                                    </MenuItem>
                                                </Menu>
                                            </div>
                                        </Form.Group>
                                    </Col>
                                    <Col md={6} className="d-flex align-items-center">
                                        <Form.Group>
                                            <Form.Label className="text-primary-dark me-3">ByPass NCE</Form.Label>
                                            <Form.Check
                                                type="switch"
                                                checked={bypassNCE}
                                                onChange={handleBypassNCEChange}
                                                inline
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>
                            </>}
                        </Form>
                    </Modal.Body>
                    <Modal.Footer>
                        <Button variant="text" onClick={handleRequestClose}>
                            Close
                        </Button>
                        <Button variant="contained" onClick={handleUpdatePayment}>
                            Update
                        </Button>
                    </Modal.Footer>
                </Modal>
            </div>
        </div>
    )
}

export default MISPaymentLinkRequest;