import React, { useEffect, useState } from "react";
import { FetchHealthRenewalPaymentLinkService, SetHealthRenewalPaymentLinkService, FetchAgentPredefinedUrl } from "../../store/actions/CommonAction";
import "react-toastify/dist/ReactToastify.css";
import { Button, ButtonGroup, TextField } from "@mui/material";
import { Form, Modal } from 'react-bootstrap';
import { Card, CardBody, Row, Col } from "reactstrap";
import { ToastContainer, toast } from 'react-toastify';


const ViewRequestDetails = (props) => {
    const { Id, RequestPanel } = props;
    let [showRejectModal, setshowRejectModal] = useState(false);
    let [showL2Modal, setshowL2Modal] = useState(false);
    let [remarks, setremarks] = useState('');
    let [L2reason, setL2reason] = useState("");
    let [detailsData, setdetailsData] = useState({});

    useEffect(() => {
        if (Id > 0) {
            FetchHealthRenewalPaymentLinkService({ Type: 2, ID: Id }, function (resultData) {
                if (resultData && resultData.data.data && Array.isArray(resultData.data.data[0])) {
                    setdetailsData(resultData.data.data[0][0]);
                }
            })
        }
    }, []);

    const handleAccept = () => {
        let type = 4;
        if (RequestPanel == "L2Request")
            type = 6
        SetHealthRenewalPaymentLinkService({ Type: type, ID: Id }, function (resultData) {
            if (resultData && resultData.data.data) {
                console.log(resultData);
                alert("Request Accepted!");
                window.location.reload();

            }
        })
    }

    const handleReject = () => {
        setshowRejectModal(true)
    }

    const onRejectClose = () => {
        setshowRejectModal(false)
    }

    const handleRemarksChange = (e) => {
        setremarks(e.target.value);
    };

    const SubmitReject = () => {
        if (remarks && remarks != '') {
            SetHealthRenewalPaymentLinkService(
                {
                    Type: 3
                    , ID: Id
                    , CancellationReason: remarks
                },
                function (resultData) {
                    if (resultData && resultData.data.data) {
                        console.log(resultData);
                        alert("Request Rejected!");
                        window.location.reload();
                    }
                })
        }
        else {
            toast("Please enter remarks.", { type: 'error' });
        }
    };

    const handleL2 = () => {
        setshowL2Modal(true)
    }

    const onL2Close = () => {
        setshowL2Modal(false)
    }

    const handleResonChange = (e) => {
        setL2reason(e.target.value);
    };

    const SubmitL2Reuest = () => {
        if (L2reason != "") {
            SetHealthRenewalPaymentLinkService(
                {
                    Type: 5
                    , ID: Id
                    , L2Reason: remarks
                    , L2ReasonId: L2reason
                },
                function (resultData) {
                    if (resultData && resultData.data.data) {
                        console.log(resultData);
                        alert("Send for Approval!");
                        window.location.reload();
                    }
                })
        }
        else{
            toast("Please select reason.", { type: 'error' });
        }
    };

    const handleViewAttachedFile = () => {
        FetchAgentPredefinedUrl(Id, function (resultData) {
            try {
                if (resultData != null && resultData.data.data) {
                    ViewAgentFile(resultData.data.data);
                }
                else {
                    toast('No file Attached', { type: 'success' });
                }
            }
            catch (e) {
                toast(`${e}`, { type: 'error' });
            }
        });
    }

    const ViewAgentFile = (filepath) => {
        if (filepath && filepath != "")
            window.open(filepath)
        else
            toast(`Corrupted File!`, { type: 'error' });
    }

    return (
        <div >
            <ToastContainer />
            <Row>
                <Col md="12">
                    <Form>
                        <Card>
                            <CardBody>
                                <Row>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">LeadID</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={detailsData.LeadID ? detailsData.LeadID : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Supplier Name</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={detailsData.InsurerName ? detailsData.InsurerName : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">ProposerName</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={detailsData.ProposerName ? detailsData.ProposerName : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <br />
                                <Row>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Amount (Notice Premium)</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={detailsData.PremiumAmount ? detailsData.PremiumAmount : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark"> Name</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={detailsData.ProposerName ? detailsData.ProposerName : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>

                                    <Col>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark"> PehchanID</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={detailsData.PehchanID ? detailsData.PehchanID : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                </Row>
                                <br />
                                <Row>
                                    <Col md={RequestPanel == "Supervisor" ? 8 : 4}>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark"> PaymentReason</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={detailsData.PaymentReason ? detailsData.PaymentReason : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    <Col md={4}>
                                        <Form.Group as={Col} md={12} >
                                            <Form.Label className="text-primary-dark">Policy No</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={detailsData.PolicyNo ? detailsData.PolicyNo : "N/A"}
                                                readOnly
                                            />
                                        </Form.Group>
                                    </Col>
                                    {RequestPanel != "Supervisor" &&
                                        <Col md={4}>
                                            <Form.Group as={Col} md={12} >
                                                <Form.Label className="text-primary-dark">L2 Approval Reason</Form.Label>
                                                <Form.Control
                                                    type="text"
                                                    value={detailsData.L2ApprovalReason ? detailsData.L2ApprovalReason : "N/A"}
                                                    readOnly
                                                />
                                            </Form.Group>
                                        </Col>
                                    }
                                </Row>
                                <br />
                                <Row>
                                    <Col>
                                        <div className="d-flex justify-content-center" md={12}>
                                            <ButtonGroup>
                                                <ButtonGroup aria-label="Basic example">
                                                    <Button fullWidth variant="contained" className="btn btnSmall" color="warning" onClick={() => handleViewAttachedFile()}>
                                                        View Agent Attached File
                                                    </Button>
                                                </ButtonGroup>
                                            </ButtonGroup>
                                        </div>
                                    </Col><Col>
                                        <div className="d-flex justify-content-center" md={12}>
                                            <ButtonGroup>
                                                <ButtonGroup aria-label="Basic example">
                                                    <Button fullWidth variant="contained" className="btn btnSmall" color="success" onClick={() => handleAccept()}>
                                                        Approve
                                                    </Button>
                                                </ButtonGroup>
                                            </ButtonGroup>
                                        </div>
                                    </Col>
                                    <Col>
                                        <div className="d-flex justify-content-center" md={12}>
                                            <ButtonGroup>

                                                <ButtonGroup aria-label="Basic example">
                                                    <Button fullWidth variant="contained" className="btn btnSmall" color="error" onClick={() => handleReject()}>
                                                        Reject
                                                    </Button>
                                                </ButtonGroup>
                                            </ButtonGroup>
                                        </div>
                                    </Col>
                                    {RequestPanel == "Supervisor" &&
                                        <Col>
                                            <div className="d-flex justify-content-center" md={12}>
                                                <ButtonGroup>
                                                    <ButtonGroup aria-label="Basic example">
                                                        <Button fullWidth variant="contained" className="btn btnSmall" color="secondary" onClick={() => handleL2()}>
                                                            Send to L2
                                                        </Button>
                                                    </ButtonGroup>
                                                </ButtonGroup>
                                            </div>
                                        </Col>
                                    }
                                </Row>
                            </CardBody>
                        </Card>
                    </Form>
                </Col>
            </Row >
            <>
                <Modal show={showRejectModal} onHide={onRejectClose} dialogClassName="modal-90w">
                    <Modal.Header closeButton>
                        <Modal.Title>Reason for rejection</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <Form.Label className="text-primary-dark">Remarks</Form.Label>
                        <ToastContainer />
                        <TextField
                            multiline
                            fullWidth
                            minRows={4}
                            variant="outlined"
                            placeholder="Enter remarks"
                            value={remarks}
                            onChange={handleRemarksChange}
                        />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button onClick={SubmitReject} variant="contained" color="primary">
                            Submit
                        </Button>
                    </Modal.Footer>
                </Modal>
            </>
            <>
                <Modal show={showL2Modal} onHide={onL2Close} dialogClassName="modal-90w">
                    <Modal.Header closeButton>
                        <Modal.Title>Submit for L2 Approval</Modal.Title>
                    </Modal.Header>
                    <Modal.Body>
                        <ToastContainer />
                        <div className="form-group">
                            <label htmlFor="criteria" className="form-label">
                                *Select Reason
                            </label>
                            <select
                                className="form-select"
                                value={L2reason}
                                onChange={handleResonChange}
                            >
                                <option value="">Select</option>
                                <option value="NCE_Approval">NCE Approval</option>
                                <option value="MM_Approval">MM Approval</option>
                                <option value="Quarterly_approval">Quarterly approval</option>
                                <option value="categories_links">All categories with links</option>
                            </select>
                        </div>
                        <Form.Label className="text-primary-dark">Remarks</Form.Label>
                        <TextField
                            multiline
                            fullWidth
                            minRows={4}
                            variant="outlined"
                            placeholder="Enter remarks"
                            value={remarks}
                            onChange={handleRemarksChange}
                        />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button onClick={SubmitL2Reuest} variant="contained" color="primary">
                            Submit
                        </Button>
                    </Modal.Footer>
                </Modal>
            </>
        </div >
    );

}

export default ViewRequestDetails;